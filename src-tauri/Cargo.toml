[package]
name = "mmtodo"
version = "1.0.0"
description = "离线待办事项应用"
authors = ["mmtodo"]
license = "MIT"
repository = ""
edition = "2021"
rust-version = "1.71"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "mmtodo_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.3.0", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
log = "0.4"
tauri = { version = "2.3.0", features = [] }
tauri-plugin-log = "2.3.0"
tauri-plugin-fs = "2.3.0"
