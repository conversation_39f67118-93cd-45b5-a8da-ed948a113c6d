{"rustc": 12610991425282158916, "features": "[\"default\", \"rwh_06\"]", "declared_features": "[\"all\", \"api-level-23\", \"api-level-24\", \"api-level-25\", \"api-level-26\", \"api-level-27\", \"api-level-28\", \"api-level-29\", \"api-level-30\", \"api-level-31\", \"api-level-32\", \"api-level-33\", \"audio\", \"bitmap\", \"default\", \"jni\", \"media\", \"nativewindow\", \"rwh_04\", \"rwh_05\", \"rwh_06\", \"sync\", \"test\"]", "target": 4479777621832623082, "profile": 15657897354478470176, "path": 5626804840147145249, "deps": [[2395061796398438895, "ffi", false, 3730532124991286285], [4143744114649553716, "rwh_06", false, 15676670523756598669], [5986029879202738730, "log", false, 13213076843211393790], [7896293946984509699, "bitflags", false, 13385698685201893701], [8008191657135824715, "thiserror", false, 9276836988202884701], [15987728108628015046, "jni_sys", false, 13435991201941162259], [16712258961403650142, "num_enum", false, 7502787338762240661]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/ndk-c3abc3b129001ebd/dep-lib-ndk", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 4453010800046437048, "compile_kind": 6373778340919622063}