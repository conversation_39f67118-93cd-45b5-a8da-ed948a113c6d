{"rustc": 12610991425282158916, "features": "[\"__common\", \"futures-core\", \"futures-util\", \"pin-project-lite\", \"sync_wrapper\", \"timeout\", \"tokio\", \"util\"]", "declared_features": "[\"__common\", \"balance\", \"buffer\", \"discover\", \"filter\", \"full\", \"futures-core\", \"futures-util\", \"hdrhistogram\", \"hedge\", \"indexmap\", \"limit\", \"load\", \"load-shed\", \"log\", \"make\", \"pin-project-lite\", \"ready-cache\", \"reconnect\", \"retry\", \"slab\", \"spawn-ready\", \"steer\", \"sync_wrapper\", \"timeout\", \"tokio\", \"tokio-stream\", \"tokio-util\", \"tracing\", \"util\"]", "target": 12249542225364378818, "profile": 15657897354478470176, "path": 2118565187405275510, "deps": [[784494742817713399, "tower_service", false, 3898341329020220653], [1906322745568073236, "pin_project_lite", false, 15555291021184680713], [2517136641825875337, "sync_wrapper", false, 15526898519240275154], [7620660491849607393, "futures_core", false, 16539879136189828144], [7712452662827335977, "tower_layer", false, 14750924238251208723], [10629569228670356391, "futures_util", false, 192321553486276393], [12393800526703971956, "tokio", false, 5135354426443488023]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/tower-1b673c55a093d0e6/dep-lib-tower", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 4453010800046437048, "compile_kind": 6373778340919622063}