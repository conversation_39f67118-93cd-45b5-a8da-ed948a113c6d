{"rustc": 12610991425282158916, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1677134433092527515, "path": 833379537803273617, "deps": [[2883436298747778685, "pki_types", false, 7425488865562329932], [3722963349756955755, "once_cell", false, 7672484197163311932], [5491919304041016563, "ring", false, 2892122493608463188], [6528079939221783635, "zeroize", false, 11683983093416705314], [16400140949089969347, "build_script_build", false, 3868048572700178083], [17003143334332120809, "subtle", false, 5775742945291850877], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 2713492001348720073]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/rustls-d34c4c848438a19d/dep-lib-rustls", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 4453010800046437048, "compile_kind": 6373778340919622063}