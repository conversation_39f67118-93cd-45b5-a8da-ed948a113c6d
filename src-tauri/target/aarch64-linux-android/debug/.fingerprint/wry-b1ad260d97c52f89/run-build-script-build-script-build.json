{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9141053277961803901, "build_script_build", false, 2690879097640108621]], "local": [{"RerunIfChanged": {"output": "aarch64-linux-android/debug/build/wry-b1ad260d97c52f89/output", "paths": ["src/android/kotlin", "/Users/<USER>/Desktop/git/todo_electron/src-tauri/gen/android/app/src/main/java/com/mmtodo/app/generated/RustWebViewClient.kt", "/Users/<USER>/Desktop/git/todo_electron/src-tauri/gen/android/app/src/main/java/com/mmtodo/app/generated/PermissionHelper.kt", "/Users/<USER>/Desktop/git/todo_electron/src-tauri/gen/android/app/src/main/java/com/mmtodo/app/generated/WryActivity.kt", "/Users/<USER>/Desktop/git/todo_electron/src-tauri/gen/android/app/src/main/java/com/mmtodo/app/generated/RustWebView.kt", "/Users/<USER>/Desktop/git/todo_electron/src-tauri/gen/android/app/src/main/java/com/mmtodo/app/generated/proguard-wry.pro", "/Users/<USER>/Desktop/git/todo_electron/src-tauri/gen/android/app/src/main/java/com/mmtodo/app/generated/RustWebChromeClient.kt", "/Users/<USER>/Desktop/git/todo_electron/src-tauri/gen/android/app/src/main/java/com/mmtodo/app/generated/Logger.kt", "/Users/<USER>/Desktop/git/todo_electron/src-tauri/gen/android/app/src/main/java/com/mmtodo/app/generated/Ipc.kt"]}}, {"RerunIfEnvChanged": {"var": "WRY_ANDROID_PACKAGE", "val": "com.mmtodo.app"}}, {"RerunIfEnvChanged": {"var": "WRY_ANDROID_LIBRARY", "val": "mmtodo_lib"}}, {"RerunIfEnvChanged": {"var": "WRY_ANDROID_KOTLIN_FILES_OUT_DIR", "val": "/Users/<USER>/Desktop/git/todo_electron/src-tauri/gen/android/app/src/main/java/com/mmtodo/app/generated"}}, {"RerunIfEnvChanged": {"var": "WRY_RUSTWEBVIEWCLIENT_CLASS_EXTENSION", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_RUSTWEBVIEWCLIENT_CLASS_INIT", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_PERMISSIONH<PERSON>PER_CLASS_EXTENSION", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_PERMISSIONHELPER_CLASS_INIT", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_WRYACTIVITY_CLASS_EXTENSION", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_WRYACTIVITY_CLASS_INIT", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_RUSTWEBVIEW_CLASS_EXTENSION", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_RUSTWEBVIEW_CLASS_INIT", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_PROGUARD-WRY_CLASS_EXTENSION", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_PROGUARD-WRY_CLASS_INIT", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_RUSTWEBCHROMECLIENT_CLASS_EXTENSION", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_RUSTWEBCHROMECLIENT_CLASS_INIT", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_LOGGER_CLASS_EXTENSION", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_LOGGER_CLASS_INIT", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_IPC_CLASS_EXTENSION", "val": null}}, {"RerunIfEnvChanged": {"var": "WRY_IPC_CLASS_INIT", "val": null}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 0, "compile_kind": 0}