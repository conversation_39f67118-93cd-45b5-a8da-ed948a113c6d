{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 15657897354478470176, "path": 18128185549993732325, "deps": [[2828590642173593838, "cfg_if", false, 1651782034589859724], [4684437522915235464, "libc", false, 1042467814216040230]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/getrandom-b12513bfea9b3975/dep-lib-getrandom", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 4453010800046437048, "compile_kind": 6373778340919622063}