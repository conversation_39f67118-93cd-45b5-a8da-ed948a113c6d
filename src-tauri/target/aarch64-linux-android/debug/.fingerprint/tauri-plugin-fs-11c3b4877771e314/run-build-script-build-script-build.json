{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 18086103578690266292], [6416823254013318197, "build_script_build", false, 9557364304862258670]], "local": [{"RerunIfChanged": {"output": "aarch64-linux-android/debug/build/tauri-plugin-fs-11c3b4877771e314/output", "paths": ["permissions", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/proguard-rules.pro", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/.gitignore", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build.gradle.kts", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/androidTest/java/app/tauri/ExampleInstrumentedTest.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/test/java/app/tauri/ExampleUnitTest.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/AndroidManifest.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/PathPlugin.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/PermissionState.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/PermissionHelper.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/PluginManager.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/Plugin.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/Invoke.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/PluginHandle.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/JSArray.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/JSObject.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/PluginResult.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/PluginMethodData.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/Channel.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/InvalidPluginMethodException.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/annotation/InvokeArg.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/annotation/TauriPlugin.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/annotation/ActivityCallback.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/annotation/PluginMethod.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/annotation/Permission.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/annotation/PermissionCallback.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/FsUtils.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/Logger.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/JniMethod.kt"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}, {"RerunIfEnvChanged": {"var": "DEP_TAURI_ANDROID_LIBRARY_PATH", "val": null}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 0, "compile_kind": 0}