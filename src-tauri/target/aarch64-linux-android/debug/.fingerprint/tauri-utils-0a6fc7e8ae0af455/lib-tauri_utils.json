{"rustc": 12610991425282158916, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 1182957245363167519, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 100741169245694766], [3150220818285335163, "url", false, 10601878304110467676], [3191507132440681679, "serde_untagged", false, 2731777332934271237], [4071963112282141418, "serde_with", false, 5401085134516972054], [4899080583175475170, "semver", false, 11571157713569567551], [5986029879202738730, "log", false, 13213076843211393790], [6606131838865521726, "ctor", false, 10060210717717249272], [7170110829644101142, "json_patch", false, 9136041773680134722], [8319709847752024821, "uuid", false, 6585328026775972496], [9010263965687315507, "http", false, 3222072670832338434], [9451456094439810778, "regex", false, 4030859560802586570], [9556762810601084293, "brotli", false, 4330382968167368287], [9689903380558560274, "serde", false, 9861892732093998321], [10806645703491011684, "thiserror", false, 4723415133198370035], [11989259058781683633, "dunce", false, 15761124260472286967], [13625485746686963219, "anyhow", false, 1564234503346482556], [15367738274754116744, "serde_json", false, 10797551652286015442], [15609422047640926750, "toml", false, 7703831756070195935], [15622660310229662834, "walkdir", false, 17030502375709919593], [15932120279885307830, "memchr", false, 11523121698633883413], [17146114186171651583, "infer", false, 15049009303577603992], [17155886227862585100, "glob", false, 13194523811992035812], [17186037756130803222, "phf", false, 9819935875194560247]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/tauri-utils-0a6fc7e8ae0af455/dep-lib-tauri_utils", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 4453010800046437048, "compile_kind": 6373778340919622063}