{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 13207670262617963380]], "local": [{"RerunIfChanged": {"output": "aarch64-linux-android/debug/build/tauri-68fb7d6cb0e13a63/output", "paths": ["mobile/android-codegen", "/Users/<USER>/Desktop/git/todo_electron/src-tauri/gen/android/app/src/main/java/com/mmtodo/app/generated/TauriActivity.kt"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 0, "compile_kind": 0}