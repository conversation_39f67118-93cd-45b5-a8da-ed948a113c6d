{"rustc": 12610991425282158916, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 15657897354478470176, "path": 6583973324541947675, "deps": [[784494742817713399, "tower_service", false, 3898341329020220653], [1906322745568073236, "pin_project_lite", false, 15555291021184680713], [4121350475192885151, "iri_string", false, 1287461159719937468], [5695049318159433696, "tower", false, 15469419972648464599], [7712452662827335977, "tower_layer", false, 14750924238251208723], [7896293946984509699, "bitflags", false, 13385698685201893701], [9010263965687315507, "http", false, 3222072670832338434], [10629569228670356391, "futures_util", false, 192321553486276393], [14084095096285906100, "http_body", false, 9496681589033511555], [16066129441945555748, "bytes", false, 8367637392276907136]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/tower-http-18dfce7f69b4b50f/dep-lib-tower_http", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 4453010800046437048, "compile_kind": 6373778340919622063}