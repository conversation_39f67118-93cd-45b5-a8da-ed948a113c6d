{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 15657897354478470176, "path": 6582887944725019788, "deps": [[2828590642173593838, "cfg_if", false, 1651782034589859724], [4684437522915235464, "libc", false, 1042467814216040230], [5491919304041016563, "build_script_build", false, 17548467705134058321], [8995469080876806959, "untrusted", false, 6321391495554032346], [9920160576179037441, "getrandom", false, 16597313416078385542]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/ring-9bc097648352529b/dep-lib-ring", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 4453010800046437048, "compile_kind": 6373778340919622063}