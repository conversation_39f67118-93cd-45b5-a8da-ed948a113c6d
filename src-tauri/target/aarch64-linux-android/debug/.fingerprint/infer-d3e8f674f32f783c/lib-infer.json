{"rustc": 12610991425282158916, "features": "[\"alloc\", \"cfb\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"cfb\", \"default\", \"std\"]", "target": 17568545270158767465, "profile": 15657897354478470176, "path": 14774477577395943252, "deps": [[3381430663848060157, "cfb", false, 9521848208720213389]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/infer-d3e8f674f32f783c/dep-lib-infer", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 4453010800046437048, "compile_kind": 6373778340919622063}