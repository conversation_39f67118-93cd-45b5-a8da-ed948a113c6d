{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"rustls-tls\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 9047850945541902088, "deps": [[40386456601120721, "percent_encoding", false, 9772173714061075857], [1200537532907108615, "url<PERSON><PERSON>n", false, 100741169245694766], [2671782512663819132, "tauri_utils", false, 15939220266637129901], [3150220818285335163, "url", false, 10601878304110467676], [3331586631144870129, "getrandom", false, 13598882924568351457], [4143744114649553716, "raw_window_handle", false, 15676670523756598669], [4919829919303820331, "serialize_to_javascript", false, 5099053557246667160], [5986029879202738730, "log", false, 13213076843211393790], [6089812615193535349, "tauri_runtime", false, 163629320125763227], [7085222851776090619, "reqwest", false, 16470474987462934703], [7573826311589115053, "tauri_macros", false, 13550271128095380056], [9010263965687315507, "http", false, 3222072670832338434], [************8560274, "serde", false, 9861892732093998321], [10229185211513642314, "mime", false, 14853589050738473562], [10806645703491011684, "thiserror", false, 4723415133198370035], [11599800339996261026, "tauri_runtime_wry", false, 5794278124661607800], [11989259058781683633, "dunce", false, 15761124260472286967], [12393800526703971956, "tokio", false, 5135354426443488023], [12986574360607194341, "serde_repr", false, 16465733656482055931], [13077543566650298139, "heck", false, 12022935834092529042], [13385779688343444241, "jni", false, 6935195689360829431], [13625485746686963219, "anyhow", false, 1564234503346482556], [14039947826026167952, "build_script_build", false, 3090848662167971803], [15367738274754116744, "serde_json", false, 10797551652286015442], [16066129441945555748, "bytes", false, 8367637392276907136], [16928111194414003569, "dirs", false, 12589288955576771160], [17155886227862585100, "glob", false, 13194523811992035812]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/tauri-c20cc78c5eab9053/dep-lib-tauri", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 4453010800046437048, "compile_kind": 6373778340919622063}