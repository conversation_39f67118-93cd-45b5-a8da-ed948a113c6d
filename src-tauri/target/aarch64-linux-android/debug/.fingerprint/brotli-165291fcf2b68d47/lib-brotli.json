{"rustc": 12610991425282158916, "features": "[\"alloc-stdlib\", \"std\"]", "declared_features": "[\"alloc-stdlib\", \"benchmark\", \"billing\", \"default\", \"disable-timer\", \"disallow_large_window_size\", \"external-literal-probability\", \"ffi-api\", \"float64\", \"floating_point_context_mixing\", \"no-stdlib-ffi-binding\", \"pass-through-ffi-panics\", \"seccomp\", \"sha2\", \"simd\", \"std\", \"validation\", \"vector_scratch_space\"]", "target": 8433163163091947982, "profile": 15657897354478470176, "path": 7527815439792296236, "deps": [[9611597350722197978, "alloc_no_stdlib", false, 17475990062661157949], [16413620717702030930, "brotli_decompressor", false, 3997550719494167563], [17470296833448545982, "alloc_stdlib", false, 9491791375156012499]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/brotli-165291fcf2b68d47/dep-lib-brotli", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 4453010800046437048, "compile_kind": 6373778340919622063}