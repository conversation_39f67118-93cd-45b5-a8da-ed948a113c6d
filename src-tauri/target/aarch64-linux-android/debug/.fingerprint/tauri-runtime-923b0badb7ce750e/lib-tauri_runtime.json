{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 15191341596623046335, "deps": [[2671782512663819132, "tauri_utils", false, 15939220266637129901], [3150220818285335163, "url", false, 10601878304110467676], [4143744114649553716, "raw_window_handle", false, 15676670523756598669], [6089812615193535349, "build_script_build", false, 5153172434818713577], [7606335748176206944, "dpi", false, 15090110708432007109], [9010263965687315507, "http", false, 3222072670832338434], [9689903380558560274, "serde", false, 9861892732093998321], [10806645703491011684, "thiserror", false, 4723415133198370035], [13385779688343444241, "jni", false, 6935195689360829431], [15367738274754116744, "serde_json", false, 10797551652286015442], [16727543399706004146, "cookie", false, 8539131506495932870]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/tauri-runtime-923b0badb7ce750e/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 4453010800046437048, "compile_kind": 6373778340919622063}