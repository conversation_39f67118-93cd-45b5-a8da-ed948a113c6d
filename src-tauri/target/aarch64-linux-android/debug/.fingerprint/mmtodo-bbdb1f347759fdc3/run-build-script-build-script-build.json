{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10468200556814511282, "build_script_build", false, 298325361198749790], [14039947826026167952, "build_script_build", false, 3090848662167971803], [6416823254013318197, "build_script_build", false, 3715523860692966186], [8324462083842905811, "build_script_build", false, 2353096637650206683]], "local": [{"RerunIfChanged": {"output": "aarch64-linux-android/debug/build/mmtodo-bbdb1f347759fdc3/output", "paths": ["tauri.conf.json", "gen/android/tauri.settings.gradle", "gen/android/app/tauri.build.gradle.kts", "gen/android/app/tauri.properties", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": "{\"build\":{\"devUrl\":\"http://**************:8083/\"}}"}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 0, "compile_kind": 0}