{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 3090848662167971803], [6416823254013318197, "build_script_build", false, 9557364304862258670]], "local": [{"RerunIfChanged": {"output": "aarch64-linux-android/debug/build/tauri-plugin-fs-ef8c7bc48efd914e/output", "paths": ["permissions", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/proguard-rules.pro", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/.gitignore", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build.gradle.kts", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/generated/source/buildConfig/release/app/tauri/BuildConfig.java", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/generated/source/buildConfig/debug/app/tauri/BuildConfig.java", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/results.bin", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/JniMethod.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/FsUtils$Companion.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/FsUtils.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/PermissionState$Companion.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/JSObject$Companion.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/ChannelDeserializer$deserialize$1.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/PluginManager$channelDeserializer$1.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/PluginManager$RequestPermissionsCallback.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/JSArray$Companion.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/Channel.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/PluginManager$ActivityResultCallback.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/PluginManager.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/RequestPermissionsArgs.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/JSObject.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/CommandData.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/RemoveListenerArgs.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/InvalidCommandException.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/ChannelKt.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/JSArray.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/Plugin.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/Config.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/PluginManager$runCommand$invoke$1.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/PluginHandle.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/RegisterListenerArgs.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/Invoke.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/PluginResult.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/PluginManager$Companion.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/plugin/ChannelDeserializer.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/annotation/ActivityCallback.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/annotation/Permission.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/annotation/PermissionCallback.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/annotation/Command.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/annotation/InvokeArg.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/annotation/TauriPlugin.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/PathPlugin.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/PathPluginKt.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/PermissionState.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/GetFileNameFromUriArgs.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/Logger.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/BuildConfig.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/Logger$Companion.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/app/tauri/PermissionHelper.dex", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/.transforms/612e3f5bacfba262f3ae9677701dabc0/transformed/bundleLibRuntimeToDirDebug/desugar_graph.bin", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/last-build.bin", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/counters.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/lookups.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/classpath-snapshot/shrunk-classpath-snapshot.bin", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileDebugKotlin/local-state/build-history.bin", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/last-build.bin", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/file-to-id.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/lookups.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/lookups.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/file-to-id.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/counters.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/file-to-id.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/lookups.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/lookups.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/id-to-file.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/lookups.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/id-to-file.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/lookups.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/lookups/id-to-file.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/source-to-classes.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/internal-name-to-source.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/subtypes.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/constants.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/class-fq-name-to-source.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/java-sources-proto-map.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/class-attributes.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/proto.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/package-parts.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/jvm/kotlin/supertypes.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/inputs/source-to-output.tab", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/inputs/source-to-output.tab_i.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.keystream", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.len", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/cacheable/caches-jvm/inputs/source-to-output.tab.values.at", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/classpath-snapshot/shrunk-classpath-snapshot.bin", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/kotlin/compileReleaseKotlin/local-state/build-history.bin", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/Logger$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/PermissionState.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/Logger.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/PluginResult.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/InvalidCommandException.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/ChannelKt.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/Invoke.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/RegisterListenerArgs.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/PluginManager$ActivityResultCallback.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/Channel.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/ChannelDeserializer.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/PluginManager$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/Config.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/JSArray.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/ChannelDeserializer$deserialize$1.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/PluginManager$runCommand$invoke$1.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/CommandData.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/PluginHandle.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/JSObject.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/JSArray$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/PluginManager$channelDeserializer$1.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/PluginManager$RequestPermissionsCallback.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/RemoveListenerArgs.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/JSObject$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/Plugin.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/RequestPermissionsArgs.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/plugin/PluginManager.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/annotation/InvokeArg.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/annotation/Permission.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/annotation/TauriPlugin.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/annotation/ActivityCallback.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/annotation/Command.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/annotation/PermissionCallback.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/BuildConfig.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/PermissionHelper.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/FsUtils.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/PathPlugin.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/GetFileNameFromUriArgs.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/PermissionState$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/PathPluginKt.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/FsUtils$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/app/tauri/JniMethod.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_dir/debug/bundleLibRuntimeToDirDebug/META-INF/tauri-android_debug.kotlin_module", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/local_aar_for_lint/release/out.aar", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/default_proguard_files/global/proguard-android-optimize.txt-8.5.1", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/default_proguard_files/global/proguard-defaults.txt-8.5.1", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/default_proguard_files/global/proguard-android.txt-8.5.1", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/symbol_list_with_package_name/release/generateReleaseRFile/package-aware-r.txt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint-cache/lintVitalAnalyzeRelease/maven.google/androidx/core/group-index.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint-cache/lintVitalAnalyzeRelease/maven.google/androidx/test/ext/group-index.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint-cache/lintVitalAnalyzeRelease/maven.google/androidx/test/espresso/group-index.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint-cache/lintVitalAnalyzeRelease/maven.google/androidx/appcompat/group-index.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint-cache/lintVitalAnalyzeRelease/maven.google/com/google/android/material/group-index.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint-cache/lintVitalAnalyzeRelease/lint-cache-version.txt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint-cache/lintVitalAnalyzeRelease/sdk_index/snapshot.gz", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/full_jar/release/createFullJarRelease/full.jar", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/annotation_processor_list/release/javaPreCompileRelease/annotationProcessors.json", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/incremental/release/packageReleaseResources/compile-file-map.properties", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/incremental/release/packageReleaseResources/merger.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/incremental/packageDebugAssets/merger.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/incremental/lintVitalAnalyzeRelease/release-artifact-dependencies.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/incremental/lintVitalAnalyzeRelease/module.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/incremental/lintVitalAnalyzeRelease/release-artifact-libraries.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/incremental/lintVitalAnalyzeRelease/release.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/incremental/release-mergeJavaRes/merge-state", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/incremental/mergeDebugShaders/merger.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/incremental/packageReleaseAssets/merger.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/local_only_symbol_list/release/parseReleaseLocalResources/R-def.txt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/aapt_friendly_merged_manifests/release/processReleaseManifest/aapt/AndroidManifest.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/aapt_friendly_merged_manifests/release/processReleaseManifest/aapt/output-metadata.json", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/compile_library_classes_jar/release/bundleLibCompileToJarRelease/classes.jar", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/navigation_json/release/extractDeepLinksRelease/navigation.json", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/compile_r_class_jar/release/generateReleaseRFile/R.jar", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint_vital_lint_model/release/generateReleaseLintVitalModel/release-artifact-dependencies.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint_vital_lint_model/release/generateReleaseLintVitalModel/module.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint_vital_lint_model/release/generateReleaseLintVitalModel/release-artifact-libraries.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint_vital_lint_model/release/generateReleaseLintVitalModel/release.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/merged_java_res/release/mergeReleaseJavaResource/feature-tauri-android.jar", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/compile_symbol_list/release/generateReleaseRFile/R.txt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/annotations_typedef_file/release/extractReleaseAnnotations/typedefs.txt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/javac/release/compileReleaseJavaWithJavac/classes/app/tauri/BuildConfig.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/app/tauri/BuildConfig.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/nested_resources_validation_report/release/generateReleaseResources/nestedResourcesValidationReport.txt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint_model_metadata/release/writeReleaseLintModelMetadata/lint-model-metadata.properties", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/manifest_merge_blame_file/release/processReleaseManifest/manifest-merger-blame-release-report.txt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/java_res/release/processReleaseJavaRes/out/META-INF/tauri-android_release.kotlin_module", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/java_res/debug/processDebugJavaRes/out/META-INF/tauri-android_debug.kotlin_module", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint_model/release/generateReleaseLintModel/release-artifact-dependencies.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint_model/release/generateReleaseLintModel/module.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint_model/release/generateReleaseLintModel/release-artifact-libraries.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint_model/release/generateReleaseLintModel/release.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/aar_main_jar/release/syncReleaseLibJars/classes.jar", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/aar_metadata/release/writeReleaseAarMetadata/aar-metadata.properties", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out/lint-resources.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/merged_consumer_proguard_file/release/mergeReleaseConsumerProguardFiles/proguard.txt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/consumer_proguard_dir/release/exportReleaseConsumerProguardFiles/lib0/proguard.txt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/intermediates/runtime_library_classes_jar/release/bundleLibRuntimeToJarRelease/classes.jar", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/Logger$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/PermissionState.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/Logger.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/PluginResult.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/InvalidCommandException.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/ChannelKt.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/Invoke.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/RegisterListenerArgs.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/PluginManager$ActivityResultCallback.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/Channel.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/ChannelDeserializer.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/PluginManager$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/Config.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/JSArray.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/ChannelDeserializer$deserialize$1.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/PluginManager$runCommand$invoke$1.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/CommandData.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/PluginHandle.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/JSObject.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/JSArray$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/PluginManager$channelDeserializer$1.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/PluginManager$RequestPermissionsCallback.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/RemoveListenerArgs.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/JSObject$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/Plugin.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/RequestPermissionsArgs.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/plugin/PluginManager.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/annotation/InvokeArg.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/annotation/Permission.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/annotation/TauriPlugin.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/annotation/ActivityCallback.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/annotation/Command.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/annotation/PermissionCallback.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/PermissionHelper.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/FsUtils.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/PathPlugin.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/GetFileNameFromUriArgs.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/PermissionState$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/PathPluginKt.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/FsUtils$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/app/tauri/JniMethod.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/release/META-INF/tauri-android_release.kotlin_module", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/Logger$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/PermissionState.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/Logger.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/PluginResult.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/InvalidCommandException.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/ChannelKt.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/Invoke.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/RegisterListenerArgs.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/PluginManager$ActivityResultCallback.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/Channel.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/ChannelDeserializer.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/PluginManager$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/Config.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/JSArray.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/ChannelDeserializer$deserialize$1.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/PluginManager$runCommand$invoke$1.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/CommandData.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/PluginHandle.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/JSObject.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/JSArray$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/PluginManager$channelDeserializer$1.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/PluginManager$RequestPermissionsCallback.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/RemoveListenerArgs.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/JSObject$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/Plugin.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/RequestPermissionsArgs.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/plugin/PluginManager.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/annotation/InvokeArg.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/annotation/Permission.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/annotation/TauriPlugin.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/annotation/ActivityCallback.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/annotation/Command.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/annotation/PermissionCallback.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/PermissionHelper.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/FsUtils.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/PathPlugin.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/GetFileNameFromUriArgs.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/PermissionState$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/PathPluginKt.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/FsUtils$Companion.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/app/tauri/JniMethod.class", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/tmp/kotlin-classes/debug/META-INF/tauri-android_debug.kotlin_module", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/outputs/logs/manifest-merger-debug-report.txt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/build/outputs/logs/manifest-merger-release-report.txt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/androidTest/java/app/tauri/ExampleInstrumentedTest.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/test/java/app/tauri/ExampleUnitTest.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/AndroidManifest.xml", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/PathPlugin.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/PermissionState.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/PermissionHelper.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/PluginManager.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/Plugin.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/Invoke.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/PluginHandle.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/JSArray.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/JSObject.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/PluginResult.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/PluginMethodData.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/Channel.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/plugin/InvalidPluginMethodException.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/annotation/InvokeArg.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/annotation/TauriPlugin.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/annotation/ActivityCallback.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/annotation/PluginMethod.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/annotation/Permission.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/annotation/PermissionCallback.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/FsUtils.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/Logger.kt", "/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/tauri-2.6.2/mobile/android/src/main/java/app/tauri/JniMethod.kt"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}, {"RerunIfEnvChanged": {"var": "DEP_TAURI_ANDROID_LIBRARY_PATH", "val": null}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 0, "compile_kind": 0}