{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 3692940658369895063, "deps": [[2671782512663819132, "tauri_utils", false, 15939220266637129901], [3150220818285335163, "url", false, 10601878304110467676], [4143744114649553716, "raw_window_handle", false, 15676670523756598669], [5986029879202738730, "log", false, 13213076843211393790], [6089812615193535349, "tauri_runtime", false, 163629320125763227], [8826339825490770380, "tao", false, 3247124987979401488], [9010263965687315507, "http", false, 3222072670832338434], [9141053277961803901, "wry", false, 9397646193860487307], [11599800339996261026, "build_script_build", false, 8972388632831630741], [13385779688343444241, "jni", false, 6935195689360829431]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/tauri-runtime-wry-b171b0250b91d5fc/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 4453010800046437048, "compile_kind": 6373778340919622063}