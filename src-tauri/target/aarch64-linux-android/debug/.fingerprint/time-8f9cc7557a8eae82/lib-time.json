{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"formatting\", \"local-offset\", \"macros\", \"parsing\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"formatting\", \"large-dates\", \"local-offset\", \"macros\", \"parsing\", \"quickcheck\", \"rand\", \"serde\", \"serde-human-readable\", \"serde-well-known\", \"std\", \"wasm-bindgen\"]", "target": 3713843603798095488, "profile": 17564239539340744749, "path": 17647861187878706737, "deps": [[253581978874359338, "deranged", false, 18264991361072441804], [724804171976944018, "num_conv", false, 13127933484446337352], [1509944293013079861, "time_macros", false, 626151117647068233], [4684437522915235464, "libc", false, 1042467814216040230], [4880290578780516359, "num_threads", false, 1883168452534454316], [5901133744777009488, "powerfmt", false, 7409848518596431273], [7695812897323945497, "itoa", false, 12024097639105316729], [9886904983647127192, "time_core", false, 12333291791839540638]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/debug/.fingerprint/time-8f9cc7557a8eae82/dep-lib-time", "checksum": false}}], "rustflags": ["-Clink-arg=-landroid", "-Clink-arg=-llog", "-Clink-arg=-lOpenSLES"], "config": 4453010800046437048, "compile_kind": 6373778340919622063}