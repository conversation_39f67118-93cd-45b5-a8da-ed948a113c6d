/Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/deps/rust_decimal-1bf94b6cdd3b0fed.d: /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/constants.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/decimal.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/error.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/array.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/add.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/cmp.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/common.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/div.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/mul.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/rem.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/str.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/arithmetic_impls.rs /Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/build/rust_decimal-aeabff1e70c7578a/out/README-lib.md

/Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/deps/librust_decimal-1bf94b6cdd3b0fed.rlib: /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/constants.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/decimal.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/error.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/array.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/add.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/cmp.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/common.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/div.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/mul.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/rem.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/str.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/arithmetic_impls.rs /Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/build/rust_decimal-aeabff1e70c7578a/out/README-lib.md

/Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/deps/librust_decimal-1bf94b6cdd3b0fed.rmeta: /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/constants.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/decimal.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/error.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/array.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/add.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/cmp.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/common.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/div.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/mul.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/rem.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/str.rs /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/arithmetic_impls.rs /Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/build/rust_decimal-aeabff1e70c7578a/out/README-lib.md

/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/lib.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/constants.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/decimal.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/error.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/array.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/add.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/cmp.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/common.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/div.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/mul.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/ops/rem.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/str.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/rust_decimal-1.37.2/src/arithmetic_impls.rs:
/Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/build/rust_decimal-aeabff1e70c7578a/out/README-lib.md:

# env-dep:OUT_DIR=/Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/build/rust_decimal-aeabff1e70c7578a/out
