/Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/deps/mmtodo_lib.d: src/lib.rs /Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/build/mmtodo-bbdb1f347759fdc3/out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5

/Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/deps/libmmtodo_lib.a: src/lib.rs /Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/build/mmtodo-bbdb1f347759fdc3/out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5

/Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/deps/libmmtodo_lib.so: src/lib.rs /Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/build/mmtodo-bbdb1f347759fdc3/out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5

/Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/deps/libmmtodo_lib.rlib: src/lib.rs /Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/build/mmtodo-bbdb1f347759fdc3/out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5

src/lib.rs:
/Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/build/mmtodo-bbdb1f347759fdc3/out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5:

# env-dep:CARGO_PKG_AUTHORS=mmtodo
# env-dep:CARGO_PKG_DESCRIPTION=离线待办事项应用
# env-dep:CARGO_PKG_NAME=mmtodo
# env-dep:OUT_DIR=/Users/<USER>/Desktop/git/todo_electron/src-tauri/target/aarch64-linux-android/debug/build/mmtodo-bbdb1f347759fdc3/out
