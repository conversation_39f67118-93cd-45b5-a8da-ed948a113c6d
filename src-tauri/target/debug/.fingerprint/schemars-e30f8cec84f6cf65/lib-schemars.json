{"rustc": 12610991425282158916, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 3033921117576893, "path": 13215980526275384624, "deps": [[3150220818285335163, "url", false, 11309777604084385651], [6913375703034175521, "build_script_build", false, 16990148222287359806], [8319709847752024821, "uuid1", false, 17186898990093086609], [9122563107207267705, "dyn_clone", false, 9830279851633478312], [9689903380558560274, "serde", false, 10436299463441863810], [14923790796823607459, "indexmap", false, 12550226836474357547], [15367738274754116744, "serde_json", false, 9171660928845485525], [16071897500792579091, "schemars_derive", false, 16396525718079098069]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/schemars-e30f8cec84f6cf65/dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}