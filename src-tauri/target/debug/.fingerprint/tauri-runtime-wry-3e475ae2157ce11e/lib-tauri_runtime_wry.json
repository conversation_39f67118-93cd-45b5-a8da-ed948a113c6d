{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 5347358027863023418, "path": 3692940658369895063, "deps": [[1386409696764982933, "objc2", false, 15912878669321727044], [2671782512663819132, "tauri_utils", false, 4774193588427684518], [3150220818285335163, "url", false, 4655274393020928587], [4143744114649553716, "raw_window_handle", false, 7920172990466857377], [5986029879202738730, "log", false, 15297211796552523558], [6089812615193535349, "tauri_runtime", false, 18235953121239536489], [8826339825490770380, "tao", false, 5867796569805216843], [9010263965687315507, "http", false, 3379903794546627310], [9141053277961803901, "wry", false, 13323715642216804212], [9859211262912517217, "objc2_foundation", false, 8209916847303003122], [10575598148575346675, "objc2_app_kit", false, 15139410305181446937], [11599800339996261026, "build_script_build", false, 11044837541422052689]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-wry-3e475ae2157ce11e/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}