{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"formatting\", \"local-offset\", \"macros\", \"parsing\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"formatting\", \"large-dates\", \"local-offset\", \"macros\", \"parsing\", \"quickcheck\", \"rand\", \"serde\", \"serde-human-readable\", \"serde-well-known\", \"std\", \"wasm-bindgen\"]", "target": 3713843603798095488, "profile": 17255934022493190207, "path": 17647861187878706737, "deps": [[253581978874359338, "deranged", false, 9460988418509265472], [724804171976944018, "num_conv", false, 15510058989595364051], [1509944293013079861, "time_macros", false, 9344175290806898221], [4684437522915235464, "libc", false, 4583146466839442577], [4880290578780516359, "num_threads", false, 15119248748523917253], [5901133744777009488, "powerfmt", false, 16938206304238222623], [7695812897323945497, "itoa", false, 7791718285601308913], [9886904983647127192, "time_core", false, 7511359734447257418]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/time-9b448f6c8e336fcb/dep-lib-time", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}