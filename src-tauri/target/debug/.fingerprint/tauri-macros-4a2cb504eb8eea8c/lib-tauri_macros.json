{"rustc": 12610991425282158916, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 3033921117576893, "path": 10588049754273702291, "deps": [[2671782512663819132, "tauri_utils", false, 11439906322500531975], [3060637413840920116, "proc_macro2", false, 17665307416580705032], [4974441333307933176, "syn", false, 10545372724641783116], [13077543566650298139, "heck", false, 11011398842003003513], [14455244907590647360, "tauri_codegen", false, 7402716037176118247], [17990358020177143287, "quote", false, 9549848433394934671]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-macros-4a2cb504eb8eea8c/dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}