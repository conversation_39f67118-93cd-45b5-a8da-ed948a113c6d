{"rustc": 12610991425282158916, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 3033921117576893, "path": 11692236250562298605, "deps": [[2616743947975331138, "plist", false, 16756248161616205841], [2671782512663819132, "tauri_utils", false, 11439906322500531975], [3060637413840920116, "proc_macro2", false, 17665307416580705032], [3150220818285335163, "url", false, 11309777604084385651], [4899080583175475170, "semver", false, 1686020054815086004], [4974441333307933176, "syn", false, 10545372724641783116], [7170110829644101142, "json_patch", false, 1611996000638289759], [7392050791754369441, "ico", false, 2357735218026816232], [8319709847752024821, "uuid", false, 17186898990093086609], [9556762810601084293, "brotli", false, 10081024044290957471], [9689903380558560274, "serde", false, 10436299463441863810], [9857275760291862238, "sha2", false, 2993503911205994051], [10806645703491011684, "thiserror", false, 16483982159260058172], [12409575957772518135, "time", false, 3501941194644056137], [12687914511023397207, "png", false, 2012250986635804544], [13077212702700853852, "base64", false, 10374437624538897293], [15367738274754116744, "serde_json", false, 9171660928845485525], [15622660310229662834, "walkdir", false, 6584957902984832216], [17990358020177143287, "quote", false, 9549848433394934671]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-codegen-9e4ba96a21c2ee64/dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}