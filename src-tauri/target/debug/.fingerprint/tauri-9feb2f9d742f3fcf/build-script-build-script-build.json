{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"rustls-tls\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 5408242616063297496, "profile": 5347358027863023418, "path": 689615070440783443, "deps": [[2671782512663819132, "tauri_utils", false, 4945128464642051623], [10806952569398136823, "tauri_build", false, 16121807871046421097], [13077543566650298139, "heck", false, 11011398842003003513], [17155886227862585100, "glob", false, 3166642299656201129]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-9feb2f9d742f3fcf/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}