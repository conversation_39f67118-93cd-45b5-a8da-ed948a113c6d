{"rustc": 12610991425282158916, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 3033921117576893, "path": 18375750622479031193, "deps": [[2671782512663819132, "tauri_utils", false, 11439906322500531975], [4899080583175475170, "semver", false, 1686020054815086004], [6913375703034175521, "schemars", false, 1639867982302133289], [7170110829644101142, "json_patch", false, 1611996000638289759], [9689903380558560274, "serde", false, 10436299463441863810], [12714016054753183456, "tauri_winres", false, 3496088914879105952], [13077543566650298139, "heck", false, 11011398842003003513], [13475171727366188400, "cargo_toml", false, 2619546219342922976], [13625485746686963219, "anyhow", false, 12354448840820205032], [15367738274754116744, "serde_json", false, 9171660928845485525], [15609422047640926750, "toml", false, 13621259492791026692], [15622660310229662834, "walkdir", false, 6584957902984832216], [16928111194414003569, "dirs", false, 13405821578863030619], [17155886227862585100, "glob", false, 3166642299656201129]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-build-f77b937543f6f2ad/dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}