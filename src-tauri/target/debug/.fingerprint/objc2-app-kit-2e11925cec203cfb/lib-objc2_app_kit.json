{"rustc": 12610991425282158916, "features": "[\"AppKitDefines\", \"AppKitErrors\", \"NSATSTypesetter\", \"NSAccessibility\", \"NSAccessibilityColor\", \"NSAccessibilityConstants\", \"NSAccessibilityCustomAction\", \"NSAccessibilityCustomRotor\", \"NSAccessibilityElement\", \"NSAccessibilityProtocols\", \"NSActionCell\", \"NSAdaptiveImageGlyph\", \"NSAffineTransform\", \"NSAlert\", \"NSAlignmentFeedbackFilter\", \"NSAnimation\", \"NSAnimationContext\", \"NSAppearance\", \"NSAppleScriptExtensions\", \"NSApplication\", \"NSApplicationScripting\", \"NSArrayController\", \"NSAttributedString\", \"NSBezierPath\", \"NSBitmapImageRep\", \"NSBox\", \"NSBrowser\", \"NSBrowserCell\", \"NSButton\", \"NSButtonCell\", \"NSButtonTouchBarItem\", \"NSCIImageRep\", \"NSCachedImageRep\", \"NSCandidateListTouchBarItem\", \"NSCell\", \"NSClickGestureRecognizer\", \"NSClipView\", \"NSCollectionView\", \"NSCollectionViewCompositionalLayout\", \"NSCollectionViewFlowLayout\", \"NSCollectionViewGridLayout\", \"NSCollectionViewLayout\", \"NSCollectionViewTransitionLayout\", \"NSColor\", \"NSColorList\", \"NSColorPanel\", \"NSColorPicker\", \"NSColorPickerTouchBarItem\", \"NSColorPicking\", \"NSColorSampler\", \"NSColorSpace\", \"NSColorWell\", \"NSComboBox\", \"NSComboBoxCell\", \"NSComboButton\", \"NSControl\", \"NSController\", \"NSCursor\", \"NSCustomImageRep\", \"NSCustomTouchBarItem\", \"NSDataAsset\", \"NSDatePicker\", \"NSDatePickerCell\", \"NSDictionaryController\", \"NSDiffableDataSource\", \"NSDirection\", \"NSDockTile\", \"NSDocument\", \"NSDocumentController\", \"NSDocumentScripting\", \"NSDragging\", \"NSDraggingItem\", \"NSDraggingSession\", \"NSDrawer\", \"NSEPSImageRep\", \"NSErrors\", \"NSEvent\", \"NSFilePromiseProvider\", \"NSFilePromiseReceiver\", \"NSFileWrapperExtensions\", \"NSFont\", \"NSFontAssetRequest\", \"NSFontCollection\", \"NSFontDescriptor\", \"NSFontManager\", \"NSFontPanel\", \"NSForm\", \"NSFormCell\", \"NSGestureRecognizer\", \"NSGlyphGenerator\", \"NSGlyphInfo\", \"NSGradient\", \"NSGraphics\", \"NSGraphicsContext\", \"NSGridView\", \"NSGroupTouchBarItem\", \"NSHapticFeedback\", \"NSHelpManager\", \"NSImage\", \"NSImageCell\", \"NSImageRep\", \"NSImageView\", \"NSInputManager\", \"NSInputServer\", \"NSInterfaceStyle\", \"NSItemProvider\", \"NSKeyValueBinding\", \"NSLayoutAnchor\", \"NSLayoutConstraint\", \"NSLayoutGuide\", \"NSLayoutManager\", \"NSLevelIndicator\", \"NSLevelIndicatorCell\", \"NSMagnificationGestureRecognizer\", \"NSMatrix\", \"NSMediaLibraryBrowserController\", \"NSMenu\", \"NSMenuItem\", \"NSMenuItemBadge\", \"NSMenuItemCell\", \"NSMenuToolbarItem\", \"NSMovie\", \"NSNib\", \"NSNibConnector\", \"NSNibControlConnector\", \"NSNibDeclarations\", \"NSNibLoading\", \"NSNibOutletConnector\", \"NSObjectController\", \"NSOpenGL\", \"NSOpenGLLayer\", \"NSOpenGLView\", \"NSOpenPanel\", \"NSOutlineView\", \"NSPDFImageRep\", \"NSPDFInfo\", \"NSPDFPanel\", \"NSPICTImageRep\", \"NSPageController\", \"NSPageLayout\", \"NSPanGestureRecognizer\", \"NSPanel\", \"NSParagraphStyle\", \"NSPasteboard\", \"NSPasteboardItem\", \"NSPathCell\", \"NSPathComponentCell\", \"NSPathControl\", \"NSPathControlItem\", \"NSPersistentDocument\", \"NSPickerTouchBarItem\", \"NSPopUpButton\", \"NSPopUpButtonCell\", \"NSPopover\", \"NSPopoverTouchBarItem\", \"NSPredicateEditor\", \"NSPredicateEditorRowTemplate\", \"NSPressGestureRecognizer\", \"NSPressureConfiguration\", \"NSPreviewRepresentingActivityItem\", \"NSPrintInfo\", \"NSPrintOperation\", \"NSPrintPanel\", \"NSPrinter\", \"NSProgressIndicator\", \"NSResponder\", \"NSRotationGestureRecognizer\", \"NSRuleEditor\", \"NSRulerMarker\", \"NSRulerView\", \"NSRunningApplication\", \"NSSavePanel\", \"NSScreen\", \"NSScrollView\", \"NSScroller\", \"NSScrubber\", \"NSScrubberItemView\", \"NSScrubberLayout\", \"NSSearchField\", \"NSSearchFieldCell\", \"NSSearchToolbarItem\", \"NSSecureTextField\", \"NSSegmentedCell\", \"NSSegmentedControl\", \"NSShadow\", \"NSSharingCollaborationModeRestriction\", \"NSSharingService\", \"NSSharingServicePickerToolbarItem\", \"NSSharingServicePickerTouchBarItem\", \"NSSlider\", \"NSSliderAccessory\", \"NSSliderCell\", \"NSSliderTouchBarItem\", \"NSSound\", \"NSSpeechRecognizer\", \"NSSpeechSynthesizer\", \"NSSpellChecker\", \"NSSpellProtocol\", \"NSSplitView\", \"NSSplitViewController\", \"NSSplitViewItem\", \"NSStackView\", \"NSStatusBar\", \"NSStatusBarButton\", \"NSStatusItem\", \"NSStepper\", \"NSStepperCell\", \"NSStepperTouchBarItem\", \"NSStoryboard\", \"NSStoryboardSegue\", \"NSStringDrawing\", \"NSSwitch\", \"NSTabView\", \"NSTabViewController\", \"NSTabViewItem\", \"NSTableCellView\", \"NSTableColumn\", \"NSTableHeaderCell\", \"NSTableHeaderView\", \"NSTableRowView\", \"NSTableView\", \"NSTableViewDiffableDataSource\", \"NSTableViewRowAction\", \"NSText\", \"NSTextAlternatives\", \"NSTextAttachment\", \"NSTextAttachmentCell\", \"NSTextCheckingClient\", \"NSTextCheckingController\", \"NSTextContainer\", \"NSTextContent\", \"NSTextContentManager\", \"NSTextElement\", \"NSTextField\", \"NSTextFieldCell\", \"NSTextFinder\", \"NSTextInputClient\", \"NSTextInputContext\", \"NSTextInsertionIndicator\", \"NSTextLayoutFragment\", \"NSTextLayoutManager\", \"NSTextLineFragment\", \"NSTextList\", \"NSTextListElement\", \"NSTextRange\", \"NSTextSelection\", \"NSTextSelectionNavigation\", \"NSTextStorage\", \"NSTextStorageScripting\", \"NSTextTable\", \"NSTextView\", \"NSTextViewportLayoutController\", \"NSTintConfiguration\", \"NSTitlebarAccessoryViewController\", \"NSTokenField\", \"NSTokenFieldCell\", \"NSToolbar\", \"NSToolbarItem\", \"NSToolbarItemGroup\", \"NSTouch\", \"NSTouchBar\", \"NSTouchBarItem\", \"NSTrackingArea\", \"NSTrackingSeparatorToolbarItem\", \"NSTreeController\", \"NSTreeNode\", \"NSTypesetter\", \"NSUserActivity\", \"NSUserDefaultsController\", \"NSUserInterfaceCompression\", \"NSUserInterfaceItemIdentification\", \"NSUserInterfaceItemSearching\", \"NSUserInterfaceLayout\", \"NSUserInterfaceValidation\", \"NSView\", \"NSViewController\", \"NSVisualEffectView\", \"NSWindow\", \"NSWindowController\", \"NSWindowRestoration\", \"NSWindowScripting\", \"NSWindowTab\", \"NSWindowTabGroup\", \"NSWorkspace\", \"NSWritingToolsCoordinator\", \"NSWritingToolsCoordinatorAnimationParameters\", \"NSWritingToolsCoordinatorContext\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"libc\", \"objc2-cloud-kit\", \"objc2-core-data\", \"objc2-core-foundation\", \"objc2-core-graphics\", \"objc2-core-image\", \"objc2-quartz-core\", \"std\"]", "declared_features": "[\"AppKitDefines\", \"AppKitErrors\", \"NSATSTypesetter\", \"NSAccessibility\", \"NSAccessibilityColor\", \"NSAccessibilityConstants\", \"NSAccessibilityCustomAction\", \"NSAccessibilityCustomRotor\", \"NSAccessibilityElement\", \"NSAccessibilityProtocols\", \"NSActionCell\", \"NSAdaptiveImageGlyph\", \"NSAffineTransform\", \"NSAlert\", \"NSAlignmentFeedbackFilter\", \"NSAnimation\", \"NSAnimationContext\", \"NSAppearance\", \"NSAppleScriptExtensions\", \"NSApplication\", \"NSApplicationScripting\", \"NSArrayController\", \"NSAttributedString\", \"NSBezierPath\", \"NSBitmapImageRep\", \"NSBox\", \"NSBrowser\", \"NSBrowserCell\", \"NSButton\", \"NSButtonCell\", \"NSButtonTouchBarItem\", \"NSCIImageRep\", \"NSCachedImageRep\", \"NSCandidateListTouchBarItem\", \"NSCell\", \"NSClickGestureRecognizer\", \"NSClipView\", \"NSCollectionView\", \"NSCollectionViewCompositionalLayout\", \"NSCollectionViewFlowLayout\", \"NSCollectionViewGridLayout\", \"NSCollectionViewLayout\", \"NSCollectionViewTransitionLayout\", \"NSColor\", \"NSColorList\", \"NSColorPanel\", \"NSColorPicker\", \"NSColorPickerTouchBarItem\", \"NSColorPicking\", \"NSColorSampler\", \"NSColorSpace\", \"NSColorWell\", \"NSComboBox\", \"NSComboBoxCell\", \"NSComboButton\", \"NSControl\", \"NSController\", \"NSCursor\", \"NSCustomImageRep\", \"NSCustomTouchBarItem\", \"NSDataAsset\", \"NSDatePicker\", \"NSDatePickerCell\", \"NSDictionaryController\", \"NSDiffableDataSource\", \"NSDirection\", \"NSDockTile\", \"NSDocument\", \"NSDocumentController\", \"NSDocumentScripting\", \"NSDragging\", \"NSDraggingItem\", \"NSDraggingSession\", \"NSDrawer\", \"NSEPSImageRep\", \"NSErrors\", \"NSEvent\", \"NSFilePromiseProvider\", \"NSFilePromiseReceiver\", \"NSFileWrapperExtensions\", \"NSFont\", \"NSFontAssetRequest\", \"NSFontCollection\", \"NSFontDescriptor\", \"NSFontManager\", \"NSFontPanel\", \"NSForm\", \"NSFormCell\", \"NSGestureRecognizer\", \"NSGlyphGenerator\", \"NSGlyphInfo\", \"NSGradient\", \"NSGraphics\", \"NSGraphicsContext\", \"NSGridView\", \"NSGroupTouchBarItem\", \"NSHapticFeedback\", \"NSHelpManager\", \"NSImage\", \"NSImageCell\", \"NSImageRep\", \"NSImageView\", \"NSInputManager\", \"NSInputServer\", \"NSInterfaceStyle\", \"NSItemProvider\", \"NSKeyValueBinding\", \"NSLayoutAnchor\", \"NSLayoutConstraint\", \"NSLayoutGuide\", \"NSLayoutManager\", \"NSLevelIndicator\", \"NSLevelIndicatorCell\", \"NSMagnificationGestureRecognizer\", \"NSMatrix\", \"NSMediaLibraryBrowserController\", \"NSMenu\", \"NSMenuItem\", \"NSMenuItemBadge\", \"NSMenuItemCell\", \"NSMenuToolbarItem\", \"NSMovie\", \"NSNib\", \"NSNibConnector\", \"NSNibControlConnector\", \"NSNibDeclarations\", \"NSNibLoading\", \"NSNibOutletConnector\", \"NSObjectController\", \"NSOpenGL\", \"NSOpenGLLayer\", \"NSOpenGLView\", \"NSOpenPanel\", \"NSOutlineView\", \"NSPDFImageRep\", \"NSPDFInfo\", \"NSPDFPanel\", \"NSPICTImageRep\", \"NSPageController\", \"NSPageLayout\", \"NSPanGestureRecognizer\", \"NSPanel\", \"NSParagraphStyle\", \"NSPasteboard\", \"NSPasteboardItem\", \"NSPathCell\", \"NSPathComponentCell\", \"NSPathControl\", \"NSPathControlItem\", \"NSPersistentDocument\", \"NSPickerTouchBarItem\", \"NSPopUpButton\", \"NSPopUpButtonCell\", \"NSPopover\", \"NSPopoverTouchBarItem\", \"NSPredicateEditor\", \"NSPredicateEditorRowTemplate\", \"NSPressGestureRecognizer\", \"NSPressureConfiguration\", \"NSPreviewRepresentingActivityItem\", \"NSPrintInfo\", \"NSPrintOperation\", \"NSPrintPanel\", \"NSPrinter\", \"NSProgressIndicator\", \"NSResponder\", \"NSRotationGestureRecognizer\", \"NSRuleEditor\", \"NSRulerMarker\", \"NSRulerView\", \"NSRunningApplication\", \"NSSavePanel\", \"NSScreen\", \"NSScrollView\", \"NSScroller\", \"NSScrubber\", \"NSScrubberItemView\", \"NSScrubberLayout\", \"NSSearchField\", \"NSSearchFieldCell\", \"NSSearchToolbarItem\", \"NSSecureTextField\", \"NSSegmentedCell\", \"NSSegmentedControl\", \"NSShadow\", \"NSSharingCollaborationModeRestriction\", \"NSSharingService\", \"NSSharingServicePickerToolbarItem\", \"NSSharingServicePickerTouchBarItem\", \"NSSlider\", \"NSSliderAccessory\", \"NSSliderCell\", \"NSSliderTouchBarItem\", \"NSSound\", \"NSSpeechRecognizer\", \"NSSpeechSynthesizer\", \"NSSpellChecker\", \"NSSpellProtocol\", \"NSSplitView\", \"NSSplitViewController\", \"NSSplitViewItem\", \"NSStackView\", \"NSStatusBar\", \"NSStatusBarButton\", \"NSStatusItem\", \"NSStepper\", \"NSStepperCell\", \"NSStepperTouchBarItem\", \"NSStoryboard\", \"NSStoryboardSegue\", \"NSStringDrawing\", \"NSSwitch\", \"NSTabView\", \"NSTabViewController\", \"NSTabViewItem\", \"NSTableCellView\", \"NSTableColumn\", \"NSTableHeaderCell\", \"NSTableHeaderView\", \"NSTableRowView\", \"NSTableView\", \"NSTableViewDiffableDataSource\", \"NSTableViewRowAction\", \"NSText\", \"NSTextAlternatives\", \"NSTextAttachment\", \"NSTextAttachmentCell\", \"NSTextCheckingClient\", \"NSTextCheckingController\", \"NSTextContainer\", \"NSTextContent\", \"NSTextContentManager\", \"NSTextElement\", \"NSTextField\", \"NSTextFieldCell\", \"NSTextFinder\", \"NSTextInputClient\", \"NSTextInputContext\", \"NSTextInsertionIndicator\", \"NSTextLayoutFragment\", \"NSTextLayoutManager\", \"NSTextLineFragment\", \"NSTextList\", \"NSTextListElement\", \"NSTextRange\", \"NSTextSelection\", \"NSTextSelectionNavigation\", \"NSTextStorage\", \"NSTextStorageScripting\", \"NSTextTable\", \"NSTextView\", \"NSTextViewportLayoutController\", \"NSTintConfiguration\", \"NSTitlebarAccessoryViewController\", \"NSTokenField\", \"NSTokenFieldCell\", \"NSToolbar\", \"NSToolbarItem\", \"NSToolbarItemGroup\", \"NSTouch\", \"NSTouchBar\", \"NSTouchBarItem\", \"NSTrackingArea\", \"NSTrackingSeparatorToolbarItem\", \"NSTreeController\", \"NSTreeNode\", \"NSTypesetter\", \"NSUserActivity\", \"NSUserDefaultsController\", \"NSUserInterfaceCompression\", \"NSUserInterfaceItemIdentification\", \"NSUserInterfaceItemSearching\", \"NSUserInterfaceLayout\", \"NSUserInterfaceValidation\", \"NSView\", \"NSViewController\", \"NSVisualEffectView\", \"NSWindow\", \"NSWindowController\", \"NSWindowRestoration\", \"NSWindowScripting\", \"NSWindowTab\", \"NSWindowTabGroup\", \"NSWorkspace\", \"NSWritingToolsCoordinator\", \"NSWritingToolsCoordinatorAnimationParameters\", \"NSWritingToolsCoordinatorContext\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"gnustep-1-7\", \"gnustep-1-8\", \"gnustep-1-9\", \"gnustep-2-0\", \"gnustep-2-1\", \"libc\", \"objc2-cloud-kit\", \"objc2-core-data\", \"objc2-core-foundation\", \"objc2-core-graphics\", \"objc2-core-image\", \"objc2-quartz-core\", \"objc2-uniform-type-identifiers\", \"std\"]", "target": 18041863106907093401, "profile": 8196097686603091492, "path": 17657108993879680594, "deps": [[309970253587158206, "block2", false, 3408772109229429169], [1386409696764982933, "objc2", false, 15912878669321727044], [1517964206604611491, "objc2_quartz_core", false, 17103887047900036929], [4684437522915235464, "libc", false, 4583146466839442577], [7828294911682607782, "objc2_core_graphics", false, 2171021256501943847], [7896293946984509699, "bitflags", false, 15910661480117433476], [9859211262912517217, "objc2_foundation", false, 8209916847303003122], [10378802769730441691, "objc2_core_foundation", false, 5741401725322627222], [14310139767743755709, "objc2_core_image", false, 16936092298274949846], [15131121180101279514, "objc2_core_data", false, 6714011495490789793], [15796387676521131728, "objc2_cloud_kit", false, 3294106529241338370]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-app-kit-2e11925cec203cfb/dep-lib-objc2_app_kit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}