{"rustc": 12610991425282158916, "features": "[\"WKDownload\", \"WKDownloadDelegate\", \"WKFrameInfo\", \"WKHTTPCookieStore\", \"WKNavigation\", \"WKNavigationAction\", \"WKNavigationDelegate\", \"WKNavigationResponse\", \"WKOpenPanelParameters\", \"WKPreferences\", \"WKScriptMessage\", \"WKScriptMessageHandler\", \"WKSecurityOrigin\", \"WKUIDelegate\", \"WKURLSchemeHandler\", \"WKURLSchemeTask\", \"WKUserContentController\", \"WKUserScript\", \"WKWebView\", \"WKWebViewConfiguration\", \"WKWebpagePreferences\", \"WKWebsiteDataStore\", \"alloc\", \"bitflags\", \"block2\", \"objc2-app-kit\", \"objc2-core-foundation\", \"std\"]", "declared_features": "[\"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>View\", \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", \"DOMB<PERSON>b\", \"DOMCDATASection\", \"DOMCSS\", \"DOMCSSCharsetRule\", \"DOMCSSFontFaceRule\", \"DOMCSSImportRule\", \"DOMCSSMediaRule\", \"DOMCSSPageRule\", \"DOMCSSPrimitiveValue\", \"DOMCSSRule\", \"DOMCSSRuleList\", \"DOMCSSStyleDeclaration\", \"DOMCSSStyleRule\", \"DOMCSSStyleSheet\", \"DOMCSSUnknownRule\", \"DOMCSSValue\", \"DOMCSSValueList\", \"DOMCharacterData\", \"DOMComment\", \"DOMCore\", \"DOM<PERSON>ounter\", \"DOMDocument\", \"DOMDocumentFragment\", \"DOMDocumentType\", \"DOMElement\", \"DOMEntity\", \"DOMEntityReference\", \"DOMEvent\", \"DOMEventException\", \"<PERSON>OMEvent<PERSON>ist<PERSON>\", \"<PERSON><PERSON><PERSON>vent<PERSON>ar<PERSON>\", \"<PERSON><PERSON>E<PERSON>s\", \"<PERSON><PERSON><PERSON>x<PERSON>\", \"<PERSON><PERSON><PERSON>xtensions\", \"DOMFile\", \"<PERSON>OM<PERSON><PERSON>List\", \"<PERSON><PERSON>HT<PERSON>\", \"DOM<PERSON>MLAnchorElement\", \"DOMHTMLAppletElement\", \"DOMHTMLAreaElement\", \"DOMHTMLBRElement\", \"DOMHTMLBaseElement\", \"DOMHTMLBaseFontElement\", \"DOMHTMLBodyElement\", \"DOMHTMLButtonElement\", \"DOMHTMLCollection\", \"DOMHTMLDListElement\", \"DOMHTMLDirectoryElement\", \"DOMHTMLDivElement\", \"DOMHTMLDocument\", \"DOMHTMLElement\", \"DOMHTMLEmbedElement\", \"DOMHTMLFieldSetElement\", \"DOMHTMLFontElement\", \"DOMHTMLFormElement\", \"DOMHTMLFrameElement\", \"DOMHTMLFrameSetElement\", \"DOMHTMLHRElement\", \"DOMHTMLHeadElement\", \"DOMHTMLHeadingElement\", \"DOMHTMLHtmlElement\", \"DOMHTMLIFrameElement\", \"DOMHTMLImageElement\", \"DOMHTMLInputElement\", \"DOMHTMLLIElement\", \"DOMHTMLLabelElement\", \"DOMHTMLLegendElement\", \"DOMHTMLLinkElement\", \"DOMHTMLMapElement\", \"DOMHTMLMarqueeElement\", \"DOMHTMLMenuElement\", \"DOMHTMLMetaElement\", \"DOMHTMLModElement\", \"DOMHTMLOListElement\", \"DOMHTMLObjectElement\", \"DOMHTMLOptGroupElement\", \"DOMHTMLOptionElement\", \"DOMHTMLOptionsCollection\", \"DOMHTMLParagraphElement\", \"DOMHTMLParamElement\", \"DOMHTMLPreElement\", \"DOMHTMLQuoteElement\", \"DOMHTMLScriptElement\", \"DOMHTMLSelectElement\", \"DOMHTMLStyleElement\", \"DOMHTMLTableCaptionElement\", \"DOMHTMLTableCellElement\", \"DOMHTMLTableColElement\", \"DOMHTMLTableElement\", \"DOMHTMLTableRowElement\", \"DOMHTMLTableSectionElement\", \"DOMHTMLTextAreaElement\", \"DOMHTMLTitleElement\", \"DOMHTMLUListElement\", \"DOMImplementation\", \"DOMKeyboardEvent\", \"DOMMediaList\", \"DOMMouseEvent\", \"DOMMutationEvent\", \"DOMNamedNodeMap\", \"DOMNode\", \"DOMNodeFilter\", \"DOMNodeIterator\", \"DOMNodeList\", \"DOMObject\", \"DOMOverflowEvent\", \"DOMProcessingInstruction\", \"DOMProgressEvent\", \"DOMRGBColor\", \"DOMRange\", \"DOMRangeException\", \"DOMRanges\", \"DOMRect\", \"DOMStyleSheet\", \"DOMStyleSheetList\", \"DOMStylesheets\", \"DOMText\", \"DOMTraversal\", \"DOMTreeWalker\", \"DOMUIEvent\", \"DOMViews\", \"DOMWheelEvent\", \"DOMXPath\", \"DOMXPathException\", \"DOMXPathExpression\", \"DOMXPathNSResolver\", \"DOMXPathResult\", \"NSAttributedString\", \"WKBackForwardList\", \"WKBackForwardListItem\", \"WKContentRuleList\", \"WKContentRuleListStore\", \"WKContentWorld\", \"WKContextMenuElementInfo\", \"WKDataDetectorTypes\", \"WKDownload\", \"WKDownloadDelegate\", \"WKError\", \"WKFindConfiguration\", \"WKFindResult\", \"WKFoundation\", \"WKFrameInfo\", \"WKHTTPCookieStore\", \"WKNavigation\", \"WKNavigationAction\", \"WKNavigationDelegate\", \"WKNavigationResponse\", \"WKOpenPanelParameters\", \"WKPDFConfiguration\", \"WKPreferences\", \"WKPreviewActionItem\", \"WKPreviewActionItemIdentifiers\", \"WKPreviewElementInfo\", \"WKProcessPool\", \"WKScriptMessage\", \"WKScriptMessageHandler\", \"WKScriptMessageHandlerWithReply\", \"WKSecurityOrigin\", \"WKSnapshotConfiguration\", \"WKUIDelegate\", \"WKURLSchemeHandler\", \"WKURLSchemeTask\", \"WKUserContentController\", \"WKUserScript\", \"WKWebExtension\", \"WKWebExtensionAction\", \"WKWebExtensionCommand\", \"WKWebExtensionContext\", \"WKWebExtensionController\", \"WKWebExtensionControllerConfiguration\", \"WKWebExtensionControllerDelegate\", \"WKWebExtensionDataRecord\", \"WKWebExtensionDataType\", \"WKWebExtensionMatchPattern\", \"WKWebExtensionMessagePort\", \"WKWebExtensionPermission\", \"WKWebExtensionTab\", \"WKWebExtensionTabConfiguration\", \"WKWebExtensionWindow\", \"WKWebExtensionWindowConfiguration\", \"WKWebView\", \"WKWebViewConfiguration\", \"WKWebpagePreferences\", \"WKWebsiteDataRecord\", \"WKWebsiteDataStore\", \"WKWindowFeatures\", \"WebArchive\", \"WebBackForwardList\", \"WebDOMOperations\", \"WebDataSource\", \"WebDocument\", \"WebDownload\", \"WebEditingDelegate\", \"WebFrame\", \"WebFrameLoadDelegate\", \"WebFrameView\", \"WebHistory\", \"WebHistoryItem\", \"WebKitAvailability\", \"WebKitErrors\", \"WebKitLegacy\", \"WebPlugin\", \"WebPluginContainer\", \"WebPluginViewFactory\", \"WebPolicyDelegate\", \"WebPreferences\", \"WebResource\", \"WebResourceLoadDelegate\", \"WebScriptObject\", \"WebUIDelegate\", \"WebView\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"objc2-app-kit\", \"objc2-core-foundation\", \"objc2-javascript-core\", \"objc2-security\", \"std\"]", "target": 10360698204467479185, "profile": 8196097686603091492, "path": 13114536369350740590, "deps": [[309970253587158206, "block2", false, 3408772109229429169], [1386409696764982933, "objc2", false, 15912878669321727044], [7896293946984509699, "bitflags", false, 15910661480117433476], [9859211262912517217, "objc2_foundation", false, 8209916847303003122], [10378802769730441691, "objc2_core_foundation", false, 5741401725322627222], [10575598148575346675, "objc2_app_kit", false, 15139410305181446937]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-web-kit-f031e1f949d7c71e/dep-lib-objc2_web_kit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}