{"rustc": 12610991425282158916, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 5347358027863023418, "path": 5473473895138048732, "deps": [[5466618496199522463, "crc32fast", false, 15828419717990850047], [7636735136738807108, "miniz_oxide", false, 5351371854643105762]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-c340be5ecf7049b8/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}