{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 5347358027863023418, "path": 15191341596623046335, "deps": [[2671782512663819132, "tauri_utils", false, 4774193588427684518], [3150220818285335163, "url", false, 4655274393020928587], [4143744114649553716, "raw_window_handle", false, 7920172990466857377], [6089812615193535349, "build_script_build", false, 6999855241137216371], [7606335748176206944, "dpi", false, 10655700150475686794], [9010263965687315507, "http", false, 3379903794546627310], [9689903380558560274, "serde", false, 14066115008572014204], [10806645703491011684, "thiserror", false, 16483982159260058172], [15367738274754116744, "serde_json", false, 17506800863905128091], [16727543399706004146, "cookie", false, 11415289617797892631]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-1f59cfadf890ba64/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}