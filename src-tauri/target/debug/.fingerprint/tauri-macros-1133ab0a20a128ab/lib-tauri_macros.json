{"rustc": 12610991425282158916, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 5347358027863023418, "path": 10588049754273702291, "deps": [[2671782512663819132, "tauri_utils", false, 4945128464642051623], [3060637413840920116, "proc_macro2", false, 3838302064390292716], [4974441333307933176, "syn", false, 170607152562308427], [13077543566650298139, "heck", false, 11011398842003003513], [14455244907590647360, "tauri_codegen", false, 13402016746990438917], [17990358020177143287, "quote", false, 7205985961300061594]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-macros-1133ab0a20a128ab/dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}