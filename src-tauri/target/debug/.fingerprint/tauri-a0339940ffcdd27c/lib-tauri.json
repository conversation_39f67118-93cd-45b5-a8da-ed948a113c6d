{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"native-tls\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 5347358027863023418, "path": 9047850945541902088, "deps": [[40386456601120721, "percent_encoding", false, 5176345087037698122], [1200537532907108615, "url<PERSON><PERSON>n", false, 16273797958247079546], [1386409696764982933, "objc2", false, 15912878669321727044], [2616743947975331138, "plist", false, 15046951264322599493], [2671782512663819132, "tauri_utils", false, 4774193588427684518], [3150220818285335163, "url", false, 4655274393020928587], [3331586631144870129, "getrandom", false, 12679027399410634056], [4143744114649553716, "raw_window_handle", false, 7920172990466857377], [4494683389616423722, "muda", false, 1552386023602752806], [4919829919303820331, "serialize_to_javascript", false, 13334294607654458075], [5986029879202738730, "log", false, 15297211796552523558], [6089812615193535349, "tauri_runtime", false, 18235953121239536489], [7573826311589115053, "tauri_macros", false, 2420178978583411800], [8589231650440095114, "embed_plist", false, 5825805452872412957], [9010263965687315507, "http", false, 3379903794546627310], [9689903380558560274, "serde", false, 14066115008572014204], [9859211262912517217, "objc2_foundation", false, 8209916847303003122], [10229185211513642314, "mime", false, 14971428244112451935], [10575598148575346675, "objc2_app_kit", false, 15139410305181446937], [10806645703491011684, "thiserror", false, 16483982159260058172], [11599800339996261026, "tauri_runtime_wry", false, 3174643144548665029], [11989259058781683633, "dunce", false, 2298926081630310859], [12393800526703971956, "tokio", false, 1846176153220296591], [12565293087094287914, "window_vibrancy", false, 7223503503574652209], [12986574360607194341, "serde_repr", false, 6061656863675132444], [13077543566650298139, "heck", false, 11011398842003003513], [13625485746686963219, "anyhow", false, 12354448840820205032], [14039947826026167952, "build_script_build", false, 16733562502328005140], [15367738274754116744, "serde_json", false, 17506800863905128091], [16928111194414003569, "dirs", false, 13405821578863030619], [17155886227862585100, "glob", false, 3166642299656201129]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-a0339940ffcdd27c/dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}