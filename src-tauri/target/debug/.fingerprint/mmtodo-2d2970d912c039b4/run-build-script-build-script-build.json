{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10468200556814511282, "build_script_build", false, 11988878798903266792], [14039947826026167952, "build_script_build", false, 16733562502328005140], [6416823254013318197, "build_script_build", false, 9698413472930739418], [8324462083842905811, "build_script_build", false, 8614771655271033123]], "local": [{"RerunIfChanged": {"output": "debug/build/mmtodo-2d2970d912c039b4/output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}