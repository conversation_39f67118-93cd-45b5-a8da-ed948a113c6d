{"rustc": 12610991425282158916, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 5347358027863023418, "path": 18375750622479031193, "deps": [[2671782512663819132, "tauri_utils", false, 4945128464642051623], [4899080583175475170, "semver", false, 8186412078499490115], [6913375703034175521, "schemars", false, 6326067159852034871], [7170110829644101142, "json_patch", false, 11376106714269864412], [9689903380558560274, "serde", false, 7983903928278184346], [12714016054753183456, "tauri_winres", false, 17125116552003654514], [13077543566650298139, "heck", false, 11011398842003003513], [13475171727366188400, "cargo_toml", false, 3311828096304863858], [13625485746686963219, "anyhow", false, 6730188763297914073], [15367738274754116744, "serde_json", false, 15178186784616348446], [15609422047640926750, "toml", false, 15568538155828379013], [15622660310229662834, "walkdir", false, 6584957902984832216], [16928111194414003569, "dirs", false, 13024834134892025402], [17155886227862585100, "glob", false, 3166642299656201129]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-build-c7a7db92e1b3e00e/dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}