{"rustc": 12610991425282158916, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 5347358027863023418, "path": 1182957245363167519, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 16273797958247079546], [3150220818285335163, "url", false, 4655274393020928587], [3191507132440681679, "serde_untagged", false, 17712494577731580416], [4071963112282141418, "serde_with", false, 15815888020610638963], [4899080583175475170, "semver", false, 18194736739106926574], [5986029879202738730, "log", false, 15297211796552523558], [6606131838865521726, "ctor", false, 11735808386685802930], [7170110829644101142, "json_patch", false, 6677394828317906278], [8319709847752024821, "uuid", false, 3292241891999215320], [9010263965687315507, "http", false, 3379903794546627310], [9451456094439810778, "regex", false, 6925404177966218204], [9556762810601084293, "brotli", false, 10081024044290957471], [9689903380558560274, "serde", false, 14066115008572014204], [10806645703491011684, "thiserror", false, 16483982159260058172], [11989259058781683633, "dunce", false, 2298926081630310859], [13625485746686963219, "anyhow", false, 12354448840820205032], [15367738274754116744, "serde_json", false, 17506800863905128091], [15609422047640926750, "toml", false, 3423348988869045479], [15622660310229662834, "walkdir", false, 6584957902984832216], [15932120279885307830, "memchr", false, 12166528325674465750], [17146114186171651583, "infer", false, 6409118348696926079], [17155886227862585100, "glob", false, 3166642299656201129], [17186037756130803222, "phf", false, 16094657532678763592]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-utils-df56d82f53bd9183/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}