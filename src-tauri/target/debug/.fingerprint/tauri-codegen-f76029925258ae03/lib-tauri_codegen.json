{"rustc": 12610991425282158916, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 5347358027863023418, "path": 11692236250562298605, "deps": [[2616743947975331138, "plist", false, 12864641891218666150], [2671782512663819132, "tauri_utils", false, 4945128464642051623], [3060637413840920116, "proc_macro2", false, 3838302064390292716], [3150220818285335163, "url", false, 11741197136684231856], [4899080583175475170, "semver", false, 8186412078499490115], [4974441333307933176, "syn", false, 170607152562308427], [7170110829644101142, "json_patch", false, 11376106714269864412], [7392050791754369441, "ico", false, 16869074465570456301], [8319709847752024821, "uuid", false, 7686890413656217195], [9556762810601084293, "brotli", false, 10081024044290957471], [9689903380558560274, "serde", false, 7983903928278184346], [9857275760291862238, "sha2", false, 13259391483618227833], [10806645703491011684, "thiserror", false, 8381798980964497782], [12409575957772518135, "time", false, 12000617253192113427], [12687914511023397207, "png", false, 2012250986635804544], [13077212702700853852, "base64", false, 10374437624538897293], [15367738274754116744, "serde_json", false, 15178186784616348446], [15622660310229662834, "walkdir", false, 6584957902984832216], [17990358020177143287, "quote", false, 7205985961300061594]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-codegen-f76029925258ae03/dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}