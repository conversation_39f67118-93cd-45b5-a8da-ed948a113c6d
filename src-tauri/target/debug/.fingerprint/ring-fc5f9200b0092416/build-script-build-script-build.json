{"rustc": 12610991425282158916, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 5408242616063297496, "profile": 5347358027863023418, "path": 15328205298906337029, "deps": [[14929362752282690284, "cc", false, 9878958187498243411]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ring-fc5f9200b0092416/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}