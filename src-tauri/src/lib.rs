use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tauri::{command, State};
use std::sync::Mutex;

// 待办事项数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Todo {
    pub id: String,
    pub text: String,
    pub completed: bool,
    pub priority: u8,
    pub repeat: String,
    pub due_date: Option<String>,
    pub details: String,
    pub tags: Vec<String>,
    pub created_at: String,
    pub completed_at: Option<String>,
    pub completed_dates: Vec<String>,
}

// 应用状态
type TodoStorage = Mutex<HashMap<String, Todo>>;

// Tauri命令：获取所有待办事项
#[command]
fn get_todos(storage: State<TodoStorage>) -> Vec<Todo> {
    let todos = storage.lock().unwrap();
    todos.values().cloned().collect()
}

// Tauri命令：创建待办事项
#[command]
fn create_todo(todo: Todo, storage: State<TodoStorage>) -> Todo {
    let mut todos = storage.lock().unwrap();
    todos.insert(todo.id.clone(), todo.clone());
    todo
}

// Tauri命令：更新待办事项
#[command]
fn update_todo(id: String, todo: Todo, storage: State<TodoStorage>) -> Result<Todo, String> {
    let mut todos = storage.lock().unwrap();
    if todos.contains_key(&id) {
        todos.insert(id, todo.clone());
        Ok(todo)
    } else {
        Err("Todo not found".to_string())
    }
}

// Tauri命令：删除待办事项
#[command]
fn delete_todo(id: String, storage: State<TodoStorage>) -> Result<(), String> {
    let mut todos = storage.lock().unwrap();
    if todos.remove(&id).is_some() {
        Ok(())
    } else {
        Err("Todo not found".to_string())
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .manage(TodoStorage::default())
        .plugin(tauri_plugin_fs::init())
        .setup(|app| {
            if cfg!(debug_assertions) {
                app.handle().plugin(
                    tauri_plugin_log::Builder::default()
                        .level(log::LevelFilter::Info)
                        .build(),
                )?;
            }
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            get_todos,
            create_todo,
            update_todo,
            delete_todo
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
