{"$schema": "https://schema.tauri.app/config/2.0.0-rc", "productName": "待办事项应用", "version": "1.0.0", "identifier": "com.mmtodo.app", "app": {"windows": [{"label": "main", "create": true, "url": "index.html", "dragDropEnabled": true, "center": false, "width": 1200.0, "height": 800.0, "minWidth": 800.0, "minHeight": 600.0, "resizable": true, "maximizable": true, "minimizable": true, "closable": true, "title": "待办事项应用", "fullscreen": false, "focus": true, "transparent": false, "maximized": false, "visible": true, "decorations": true, "alwaysOnBottom": false, "alwaysOnTop": false, "visibleOnAllWorkspaces": false, "contentProtected": false, "skipTaskbar": false, "titleBarStyle": "Visible", "hiddenTitle": false, "acceptFirstMouse": false, "shadow": true, "incognito": false, "zoomHotkeysEnabled": false, "browserExtensionsEnabled": false}], "security": {"freezePrototype": false, "dangerousDisableAssetCspModification": false, "assetProtocol": {"scope": [], "enable": false}, "pattern": {"use": "brownfield"}, "capabilities": []}, "macOSPrivateApi": false, "withGlobalTauri": false, "enableGTKAppId": false}, "build": {"devUrl": "http://localhost:8083/", "frontendDist": "../todo-app/dist", "beforeDevCommand": "cd todo-app && npm run serve", "beforeBuildCommand": "cd todo-app && npm run build"}, "bundle": {"active": true, "targets": "all", "createUpdaterArtifacts": false, "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "useLocalToolsDir": false, "windows": {"digestAlgorithm": null, "certificateThumbprint": null, "timestampUrl": null, "tsp": false, "webviewInstallMode": {"type": "downloadBootstrapper", "silent": true}, "allowDowngrades": true, "wix": null, "nsis": null, "signCommand": null}, "linux": {"appimage": {"bundleMediaFramework": false, "files": {}}, "deb": {"files": {}}, "rpm": {"release": "1", "epoch": 0, "files": {}}}, "macOS": {"files": {}, "minimumSystemVersion": "10.13", "hardenedRuntime": true, "dmg": {"windowSize": {"width": 660, "height": 400}, "appPosition": {"x": 180, "y": 170}, "applicationFolderPosition": {"x": 480, "y": 170}}}, "iOS": {"minimumSystemVersion": "13.0"}, "android": {"minSdkVersion": 24}}, "plugins": {"fs": {"requireLiteralLeadingDot": false}}}