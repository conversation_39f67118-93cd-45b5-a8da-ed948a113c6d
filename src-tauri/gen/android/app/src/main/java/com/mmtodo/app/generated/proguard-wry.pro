# THIS FILE IS AUTO-GENERATED. DO NOT MODIFY!!

# Copyright 2020-2023 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT

-keep class com.mmtodo.app.* {
  native <methods>;
}

-keep class com.mmtodo.app.WryActivity {
  public <init>(...);

  void setWebView(com.mmtodo.app.RustWebView);
  java.lang.Class getAppClass(...);
  java.lang.String getVersion();
}

-keep class com.mmtodo.app.Ipc {
  public <init>(...);

  @android.webkit.JavascriptInterface public <methods>;
}

-keep class com.mmtodo.app.RustWebView {
  public <init>(...);

  void loadUrlMainThread(...);
  void loadHTMLMainThread(...);
  void evalScript(...);
}

-keep class com.mmtodo.app.RustWebChromeClient,com.mmtodo.app.RustWebViewClient {
  public <init>(...);
}
