{"$schema": "https://schema.tauri.app/config/2.0.0-rc", "productName": "待办事项应用", "version": "1.0.0", "identifier": "com.mmtodo.app", "build": {"frontendDist": "../todo-app/dist", "devUrl": "http://localhost:8083", "beforeDevCommand": "cd todo-app && npm run serve", "beforeBuildCommand": "cd todo-app && npm run build"}, "app": {"windows": [{"title": "待办事项应用", "width": 1200, "height": 800, "resizable": true, "fullscreen": false, "minWidth": 800, "minHeight": 600}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "plugins": {"fs": {"requireLiteralLeadingDot": false}}}