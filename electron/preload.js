// Electron的预加载脚本
const { contextBridge, ipc<PERSON><PERSON>er } = require('electron');

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 发送消息到主进程
  send: (channel, data) => {
    ipcRenderer.send(channel, data);
  },
  // 从主进程接收消息
  receive: (channel, func) => {
    ipcRenderer.on(channel, (event, ...args) => func(...args));
  }
});

// 当DOM加载完成时
window.addEventListener('DOMContentLoaded', () => {
  console.log('DOM已加载，Electron预加载脚本执行完成');

  const replaceText = (selector, text) => {
    const element = document.getElementById(selector);
    if (element) element.innerText = text;
  };

  for (const dependency of ['chrome', 'node', 'electron']) {
    replaceText(`${dependency}-version`, process.versions[dependency]);
  }
});
