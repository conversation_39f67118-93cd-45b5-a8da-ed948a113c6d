const { spawn } = require('child_process');
const path = require('path');
const electron = require('electron');

// 设置环境变量
process.env.NODE_ENV = 'development';

// 启动Vue开发服务器
const vueProcess = spawn('npm', ['run', 'serve'], {
  cwd: path.join(__dirname, '../todo-app'),
  stdio: 'inherit'
});

// 等待Vue开发服务器启动
setTimeout(() => {
  // 启动Electron
  const electronProcess = spawn(electron, ['.'], {
    stdio: 'inherit'
  });

  // 处理Electron进程退出
  electronProcess.on('close', () => {
    // 关闭Vue开发服务器
    vueProcess.kill();
    process.exit();
  });
}, 10000); // 等待10秒，确保Vue开发服务器已启动

// 处理进程终止信号
process.on('SIGINT', () => {
  vueProcess.kill();
  process.exit();
});
