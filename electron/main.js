const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const url = require('url');

// 强制设置为生产环境，避免使用开发服务器
const isDev = false;

let mainWindow;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    title: "待办事项应用",
    webPreferences: {
      nodeIntegration: false, // 出于安全考虑，禁用Node集成
      contextIsolation: true, // 启用上下文隔离
      preload: path.join(__dirname, 'preload.js') // 预加载脚本
    }
  });

  // 加载应用 - 直接加载打包后的静态文件
  const startUrl = url.format({
    pathname: path.join(__dirname, '../todo-app/dist/index.html'),
    protocol: 'file:',
    slashes: true
  });

  mainWindow.loadURL(startUrl);

  // 禁用自动打开DevTools
  // 如果需要调试，可以通过菜单或快捷键手动打开

  // 当窗口关闭时触发
  mainWindow.on('closed', function () {
    mainWindow = null;
  });
}

// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
  createWindow();

  app.on('activate', function () {
    // On macOS it's common to re-create a window when the dock icon is clicked
    if (mainWindow === null) createWindow();
  });
});

// Quit when all windows are closed
app.on('window-all-closed', function () {
  // On macOS it is common for applications to stay active until the user quits explicitly
  if (process.platform !== 'darwin') app.quit();
});

// Handle IPC messages from renderer process
ipcMain.on('message-from-renderer', (event, arg) => {
  console.log('Received from renderer:', arg);
  event.reply('message-from-main', 'Message received!');
});
