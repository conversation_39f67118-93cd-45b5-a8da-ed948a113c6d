{"name": "todo-app", "version": "0.1.0", "private": true, "main": "electron/main.js", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "electron:serve": "vue-cli-service serve --port 8080 && electron .", "electron:build": "vue-cli-service build && electron-builder", "electron:dev": "electron ."}, "dependencies": {"core-js": "^3.8.3", "element-plus": "^2.9.7", "vue": "^3.2.13", "vue-router": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "build": {"appId": "com.electron.todo-app", "productName": "Todo App", "directories": {"output": "dist_electron"}, "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}