import { createRouter, createWebHashHistory } from 'vue-router';
import TodoListView from '../views/TodoListView.vue';
import TodoDetailView from '../views/TodoDetailView.vue';

const routes = [
  {
    path: '/',
    name: 'TodoList',
    component: TodoListView,
  },
  {
    path: '/todo/:id',
    name: 'TodoDetail',
    component: TodoDetailView,
    props: true, // Allows passing route params as props to the component
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

export default router;
