/**
 * Tauri本地存储服务 - 使用Tauri的invoke API
 * 替代浏览器localStorage，支持桌面和移动端
 */

// 检查是否在Tauri环境中
const isTauri = () => {
  return window.__TAURI__ !== undefined;
};

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
};

// Tauri API调用包装器
const invokeCommand = async (command, args = {}) => {
  if (isTauri()) {
    const { invoke } = window.__TAURI__.core;
    return await invoke(command, args);
  } else {
    // 回退到localStorage（用于开发环境）
    return handleLocalStorageFallback(command, args);
  }
};

// localStorage回退方案（开发环境）
const handleLocalStorageFallback = (command, args) => {
  const todos = JSON.parse(localStorage.getItem('todos') || '[]');

  switch (command) {
    case 'get_todos':
      return todos;
    case 'create_todo': {
      const newTodo = { ...args.todo, id: generateId(), createdAt: new Date().toISOString() };
      todos.push(newTodo);
      localStorage.setItem('todos', JSON.stringify(todos));
      return newTodo;
    }
    case 'update_todo': {
      const index = todos.findIndex(todo => todo.id === args.id);
      if (index !== -1) {
        todos[index] = { ...args.todo, id: args.id };
        localStorage.setItem('todos', JSON.stringify(todos));
        return todos[index];
      }
      throw new Error('待办事项未找到');
    }
    case 'delete_todo': {
      const deleteIndex = todos.findIndex(todo => todo.id === args.id);
      if (deleteIndex !== -1) {
        todos.splice(deleteIndex, 1);
        localStorage.setItem('todos', JSON.stringify(todos));
        return;
      }
      throw new Error('待办事项未找到');
    }
    default:
      throw new Error('未知命令');
  }
};

// 获取所有待办事项
export const getAllTodos = async () => {
  try {
    return await invokeCommand('get_todos');
  } catch (error) {
    console.error('Error getting todos:', error);
    return [];
  }
};

// 创建新的待办事项
export const createTodo = async (todo) => {
  try {
    const todoData = {
      ...todo,
      id: generateId(),
      created_at: new Date().toISOString(),
      completed_dates: todo.completedDates || []
    };

    return await invokeCommand('create_todo', { todo: todoData });
  } catch (error) {
    console.error('Error creating todo:', error);
    throw error;
  }
};

// 更新待办事项
export const updateTodo = async (id, updatedTodo) => {
  try {
    const todoData = {
      ...updatedTodo,
      id,
      completed_dates: updatedTodo.completedDates || []
    };

    return await invokeCommand('update_todo', { id, todo: todoData });
  } catch (error) {
    console.error('Error updating todo:', error);
    throw error;
  }
};

// 删除待办事项
export const deleteTodo = async (id) => {
  try {
    await invokeCommand('delete_todo', { id });
  } catch (error) {
    console.error('Error deleting todo:', error);
    throw error;
  }
};

export default {
  getAllTodos,
  createTodo,
  updateTodo,
  deleteTodo
};
