/**
 * 本地存储服务 - 替代后端API
 * 使用localStorage存储待办事项数据
 */

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
};

// 从localStorage获取所有待办事项
const getTodos = () => {
  const todos = localStorage.getItem('todos');
  return todos ? JSON.parse(todos) : [];
};

// 保存所有待办事项到localStorage
const saveTodos = (todos) => {
  localStorage.setItem('todos', JSON.stringify(todos));
};

// 获取所有待办事项
export const getAllTodos = () => {
  return new Promise((resolve) => {
    resolve(getTodos());
  });
};

// 创建新的待办事项
export const createTodo = (todo) => {
  return new Promise((resolve) => {
    const todos = getTodos();
    const newTodo = {
      ...todo,
      id: generateId(),
      createdAt: new Date().toISOString()
    };
    todos.push(newTodo);
    saveTodos(todos);
    resolve(newTodo);
  });
};

// 更新待办事项
export const updateTodo = (id, updatedTodo) => {
  return new Promise((resolve, reject) => {
    const todos = getTodos();
    const index = todos.findIndex(todo => todo.id === id);
    
    if (index !== -1) {
      todos[index] = { ...updatedTodo, id };
      saveTodos(todos);
      resolve(todos[index]);
    } else {
      reject(new Error('待办事项未找到'));
    }
  });
};

// 删除待办事项
export const deleteTodo = (id) => {
  return new Promise((resolve, reject) => {
    const todos = getTodos();
    const index = todos.findIndex(todo => todo.id === id);
    
    if (index !== -1) {
      todos.splice(index, 1);
      saveTodos(todos);
      resolve();
    } else {
      reject(new Error('待办事项未找到'));
    }
  });
};

export default {
  getAllTodos,
  createTodo,
  updateTodo,
  deleteTodo
};
