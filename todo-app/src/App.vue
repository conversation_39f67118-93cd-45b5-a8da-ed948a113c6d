<template>
  <el-container direction="vertical" style="height: 100vh;">
    <el-header style="height: 60px; padding: 0; border-bottom: 1px solid #eee; display: flex; justify-content: center; align-items: center;">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="top-tabs">
        <el-tab-pane name="incomplete" label="未完成">
          <template #label>
            <el-icon><i class="el-icon-s-home"></i></el-icon>
            <span>未完成</span>
          </template>
        </el-tab-pane>
        <el-tab-pane name="important" label="重要">
          <template #label>
            <el-icon><i class="el-icon-star-on"></i></el-icon>
            <span>重要</span>
          </template>
        </el-tab-pane>
        <el-tab-pane name="not-important" label="不重要">
          <template #label>
            <el-icon><i class="el-icon-s-flag"></i></el-icon>
            <span>不重要</span>
          </template>
        </el-tab-pane>
        <el-tab-pane name="completed" label="已完成">
          <template #label>
            <el-icon><i class="el-icon-check"></i></el-icon>
            <span>已完成</span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </el-header>
    <router-view></router-view>
  </el-container>
</template>

<script>
import { defineComponent, ref, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElTabs, ElTabPane } from 'element-plus'; // Import ElTabs and ElTabPane

export default defineComponent({
  name: 'App',
  components: {
    ElTabs,
    ElTabPane,
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const activeTab = ref('incomplete'); // Default active tab

    // Watch for route changes to update activeTab
    watch(route, (newRoute) => {
      if (newRoute.name === 'TodoList') {
        activeTab.value = newRoute.query.filter || 'incomplete';
      } else if (newRoute.name === 'TodoDetail') {
        // If on detail page, no tab should be active or keep the last active one
        // For now, we'll just not change it.
      }
    }, { immediate: true });

    const handleTabChange = (name) => {
      router.push({ path: '/', query: { filter: name } });
    };

    return {
      activeTab,
      handleTabChange,
    };
  },
});
</script>

<style>
body {
  margin: 0;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", Arial, sans-serif;
}

.el-container {
  height: 100vh;
  display: flex;
  flex-direction: column; /* Ensure vertical stacking */
}

.el-header {
  background-color: #f5f7fa;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-shrink: 0;
}

.top-tabs.el-tabs {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.top-tabs.el-tabs .el-tabs__header {
  flex-grow: 1;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: 0; /* Remove default margin */
}

.top-tabs.el-tabs .el-tabs__nav-wrap {
  margin-bottom: 0;
}

.top-tabs.el-tabs .el-tabs__nav {
  display: flex;
  width: 100%;
  border: none; /* Remove border */
}

.top-tabs.el-tabs .el-tabs__item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%; /* Make tab item fill the height */
  padding: 0; /* Remove default padding */
  color: #999;
  font-size: 12px;
}

.top-tabs.el-tabs .el-tabs__item.is-active {
  color: #409EFF;
}

.top-tabs.el-tabs .el-tabs__item .el-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.top-tabs.el-tabs .el-tabs__active-bar {
  display: none; /* Hide the active bar */
}

.top-tabs.el-tabs .el-tabs__nav-wrap::after {
  display: none; /* Hide the bottom line */
}

.top-tabs.el-tabs .el-tabs__content {
  display: none; /* Hide content panes as router-view handles content */
}
</style>
