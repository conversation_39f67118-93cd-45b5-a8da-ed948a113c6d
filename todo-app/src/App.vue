<template>
  <div class="mobile-app">
    <!-- 移动端顶部导航栏 -->
    <div class="mobile-header">
      <div class="header-left">
        <el-button
          type="text"
          @click="showFilterDrawer = true"
          class="menu-button"
        >
          <el-icon size="20">
            <i class="el-icon-s-unfold"></i>
          </el-icon>
        </el-button>
        <span class="app-title">待办事项</span>
      </div>
      <div class="header-right">
        <span class="current-date">{{ currentDateDisplay }}</span>
      </div>
    </div>

    <!-- 移动端筛选抽屉 -->
    <el-drawer
      v-model="showFilterDrawer"
      title="筛选"
      direction="ltr"
      size="280px"
    >
      <el-menu
        :default-active="activeFilterIndex"
        @select="handleFilterSelect"
        class="mobile-menu"
      >
        <el-menu-item index="1">
          <el-icon>
            <i class="el-icon-s-order"></i>
          </el-icon>
          <span>所有待办</span>
        </el-menu-item>
        <el-menu-item index="2">
          <el-icon>
            <i class="el-icon-time"></i>
          </el-icon>
          <span>未完成</span>
        </el-menu-item>
        <el-menu-item index="3">
          <el-icon>
            <i class="el-icon-star-on"></i>
          </el-icon>
          <span>重要</span>
        </el-menu-item>
        <el-menu-item index="5">
          <el-icon>
            <i class="el-icon-s-flag"></i>
          </el-icon>
          <span>不重要</span>
        </el-menu-item>
        <el-menu-item index="4">
          <el-icon>
            <i class="el-icon-check"></i>
          </el-icon>
          <span>已完成</span>
        </el-menu-item>
      </el-menu>
    </el-drawer>

    <!-- 主内容区域 -->
    <div class="mobile-main"
         :class="{ 'detail-open': selectedTodo && showDetail }">
      <!-- 搜索栏 -->
      <div class="search-container">
        <el-input
          v-model="searchTerm"
          placeholder="搜索待办事项..."
          clearable
          class="search-input"
        >
          <template #prefix>
            <el-icon>
              <i class="el-icon-search"></i>
            </el-icon>
          </template>
        </el-input>
      </div>
      <!-- 待办事项列表 -->
      <div class="todo-list-container" v-show="!selectedTodo || !showDetail">
        <div v-if="sortedTodos.length === 0" class="empty-state">
          <el-icon size="48" color="#ccc">
            <i class="el-icon-document-add"></i>
          </el-icon>
          <p>暂无待办事项</p>
          <p class="empty-hint">点击下方按钮添加新的待办事项</p>
        </div>
        <div v-else>
          <div v-for="todo in sortedTodos" :key="todo.id"
               class="mobile-todo-item"
               @click="selectTodo(todo)">
            <div class="todo-content">
              <div class="todo-main">
                <div class="todo-text" :class="{ 'completed': todo.completed }">
                  {{ todo.text }}
                </div>
                <div class="todo-meta">
                  <el-tag
                    size="small"
                    :type="getPriorityTagType(todo.priority)"
                    class="priority-tag"
                  >
                    P{{ todo.priority }}
                  </el-tag>
                  <el-tag
                    v-if="todo.repeat !== '不重复'"
                    size="small"
                    type="info"
                    class="repeat-tag"
                  >
                    {{ todo.repeat }}
                  </el-tag>
                  <span class="created-date">
                    {{ new Date(todo.createdAt).toLocaleDateString('zh-CN') }}
                  </span>
                </div>
                <div v-if="todo.tags && todo.tags.length > 0" class="todo-tags">
                  <el-tag
                    v-for="(tag, index) in todo.tags.slice(0, 3)"
                    :key="index"
                    size="small"
                    type="info"
                    class="tag-item"
                  >
                    {{ tag }}
                  </el-tag>
                  <span v-if="todo.tags.length > 3" class="more-tags">
                    +{{ todo.tags.length - 3 }}
                  </span>
                </div>
              </div>
              <div class="todo-actions">
                <el-button
                  v-if="!todo.completed"
                  type="success"
                  size="small"
                  circle
                  @click.stop="markAsCompleted(todo)"
                >
                  <el-icon>
                    <i class="el-icon-check"></i>
                  </el-icon>
                </el-button>
                <el-button
                  v-if="todo.completed"
                  type="warning"
                  size="small"
                  circle
                  @click.stop="redoTodo(todo)"
                >
                  <el-icon>
                    <i class="el-icon-refresh-left"></i>
                  </el-icon>
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  circle
                  @click.stop="deleteTodo(todo)"
                >
                  <el-icon>
                    <i class="el-icon-delete"></i>
                  </el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 移动端添加待办事项 -->
      <div class="mobile-add-todo" v-show="!selectedTodo || !showDetail">
        <div class="add-todo-main">
          <el-input
            v-model="newTodo"
            placeholder="添加新的待办事项..."
            @keyup.enter="addTodo"
            class="add-input"
            size="large"
          >
            <template #suffix>
              <el-button
                type="primary"
                @click="addTodo"
                :disabled="!newTodo.trim()"
                class="add-button"
              >
                <el-icon>
                  <i class="el-icon-plus"></i>
                </el-icon>
              </el-button>
            </template>
          </el-input>
        </div>
        <div class="add-todo-options" v-show="showAddOptions">
          <div class="option-row">
            <label>优先级:</label>
            <el-select v-model="newTodoPriority" size="small">
              <el-option v-for="item in 5" :key="item" :label="`P${item}`" :value="item"></el-option>
            </el-select>
          </div>
          <div class="option-row">
            <label>重复:</label>
            <el-select v-model="newTodoRepeat" size="small">
              <el-option v-for="item in repeatOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
        </div>
        <div class="add-todo-toggle">
          <el-button
            type="text"
            @click="showAddOptions = !showAddOptions"
            class="options-toggle"
          >
            <el-icon>
              <i class="el-icon-setting"></i>
            </el-icon>
            {{ showAddOptions ? '收起选项' : '更多选项' }}
          </el-button>
        </div>
      </div>
    </div>
    <!-- 移动端详情页面 -->
    <div class="mobile-detail"
         :class="{ 'show': selectedTodo && showDetail }"
         v-show="selectedTodo && showDetail">
      <div class="detail-header">
        <el-button
          type="text"
          @click="closeDetail"
          class="back-button"
        >
          <el-icon size="20">
            <i class="el-icon-arrow-left"></i>
          </el-icon>
        </el-button>
        <span class="detail-title">待办详情</span>
        <div class="detail-actions">
          <el-button
            v-if="!selectedTodo.completed"
            type="success"
            size="small"
            @click="markAsCompleted(selectedTodo)"
          >
            完成
          </el-button>
          <el-button
            v-if="selectedTodo.completed"
            type="warning"
            size="small"
            @click="redoTodo(selectedTodo)"
          >
            重做
          </el-button>
        </div>
      </div>

      <div class="detail-content">
        <div class="detail-section">
          <h3 class="todo-title" :class="{ 'completed': selectedTodo.completed }">
            {{ selectedTodo.text }}
          </h3>

          <div class="detail-meta">
            <div class="meta-item">
              <span class="meta-label">创建时间:</span>
              <span class="meta-value">{{ new Date(selectedTodo.createdAt).toLocaleString('zh-CN') }}</span>
            </div>
            <div v-if="selectedTodo.completed && selectedTodo.completedAt" class="meta-item">
              <span class="meta-label">完成时间:</span>
              <span class="meta-value">{{ new Date(selectedTodo.completedAt).toLocaleString('zh-CN') }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h4>基础设置</h4>
          <div class="setting-item">
            <label>优先级</label>
            <el-select v-model="selectedTodo.priority" @change="updateTodo" size="small">
              <el-option v-for="item in 5" :key="item" :label="`P${item}`" :value="item"></el-option>
            </el-select>
          </div>
          <div class="setting-item">
            <label>重复</label>
            <el-select v-model="selectedTodo.repeat" @change="updateTodo" size="small">
              <el-option v-for="item in repeatOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
          <div class="setting-item">
            <label>截止日期</label>
            <el-date-picker
              v-model="formattedDueDate"
              type="date"
              placeholder="选择日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              size="small"
              style="width: 100%;"
            />
          </div>
        </div>

        <div class="detail-section">
          <h4>标签</h4>
          <div class="tags-container">
            <el-tag
              v-for="(tag, index) in selectedTodo.tags"
              :key="index"
              closable
              @close="removeTag(index)"
              class="tag-item"
            >
              {{ tag }}
            </el-tag>
            <el-input
              v-model="newTag"
              @keyup.enter="addTag"
              placeholder="添加标签"
              size="small"
              class="tag-input"
              style="width: 120px;"
            />
          </div>
        </div>
        <div class="detail-section">
          <h4>详情</h4>
          <el-input
            v-model="selectedTodo.details"
            @change="updateTodo"
            type="textarea"
            :rows="6"
            placeholder="输入详情..."
            @keydown="handleTabKey"
            class="detail-textarea"
          />
        </div>

        <div class="detail-section">
          <el-collapse v-model="showCompletionHistory">
            <el-collapse-item title="完成历史" name="1">
              <div class="calendar-container">
                <el-calendar v-model="calendarDate" class="mobile-calendar">
                  <template #date-cell="{data}">
                    <div class="calendar-day">
                      {{ data.day.split('-').slice(2).join('-') }}
                      <div v-if="isDateCompleted(data.day)" class="completion-dot"></div>
                      <div v-if="isCreatedDate(data.day)" class="creation-dot"></div>
                    </div>
                  </template>
                </el-calendar>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>

    <!-- 浮动操作按钮 -->
    <el-button
      v-if="!selectedTodo || !showDetail"
      type="primary"
      class="fab"
      circle
      size="large"
      @click="focusAddInput"
    >
      <el-icon size="24">
        <i class="el-icon-plus"></i>
      </el-icon>
    </el-button>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, computed, nextTick } from 'vue'
import { ElDatePicker, ElCalendar } from 'element-plus'
import localStorageService from './services/localStorageService'

export default defineComponent({
  name: 'App',
  setup() {
    const newTodo = ref('')
    const newTodoPriority = ref(1)
    const newTodoRepeat = ref('不重复')
    const selectedTodo = ref(null) // 修改为null，更清晰
    const newTag = ref('')
    const activeFilterIndex = ref('2') // Default to '未完成'
    const searchTerm = ref('') // Add ref for the search term

    // 移动端相关状态
    const showFilterDrawer = ref(false)
    const showDetail = ref(false)
    const showAddOptions = ref(false)

    const repeatOptions = ref([
      { value: '不重复', label: '不重复' },
      { value: '工作日重复', label: '工作日重复' },
      { value: '每日重复', label: '每日重复' },
      { value: '周末重复', label: '周末重复' },
    ])
    const todos = ref([])

    const isWeekday = (date) => {
      const day = date.getDay();
      return day >= 1 && day <= 5; // 1-5是周一到周五
    }

    const checkRecurringTodos = () => {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // 设置为今天的开始时间

      todos.value.forEach(todo => {
        if (todo.completed && todo.completedAt) {
          const completedDate = new Date(todo.completedAt);
          completedDate.setHours(0, 0, 0, 0); // 设置为完成日期的开始时间

          // 检查是否需要重置为未完成
          let shouldReset = false;

          if (todo.repeat === '每日重复' && completedDate < today) {
            shouldReset = true;
          } else if (todo.repeat === '工作日重复' && completedDate < today && isWeekday(today)) {
            shouldReset = true;
          } else if (todo.repeat === '周末重复' && completedDate < today && !isWeekday(today)) {
            shouldReset = true;
          }

          if (shouldReset) {
            const updatedTodo = {
              ...todo,
              completed: false,
              completedAt: null
            };

            localStorageService.updateTodo(todo.id, updatedTodo)
              .then(() => {
                // 确保触发响应式更新
                const index = todos.value.findIndex(t => t.id === todo.id);
                if (index !== -1) {
                  todos.value[index] = {
                    ...todos.value[index],
                    completed: false,
                    completedAt: null
                  };
                  // 强制触发响应式更新
                  todos.value = [...todos.value];
                }
              })
              .catch(error => {
                console.error('Error resetting recurring todo:', error);
              });
          }
        }
      });
    }

    onMounted(async () => {
      try {
        const fetchedTodos = await localStorageService.getAllTodos();
        const today = new Date();
        const todayString = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`;

        const updatePromises = []; // Collect update promises

        fetchedTodos.forEach(todo => {
          if (todo.dueDate && todo.priority < 4) {
            let todoDueDateString = null;
            try {
              // Attempt to parse dueDate, assuming it might be ISO or YYYY-MM-DD
              const dueDate = new Date(todo.dueDate);
               if (!isNaN(dueDate.getTime())) {
                  todoDueDateString = `${dueDate.getFullYear()}-${(dueDate.getMonth() + 1).toString().padStart(2, '0')}-${dueDate.getDate().toString().padStart(2, '0')}`;
               } else if (typeof todo.dueDate === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(todo.dueDate)) {
                  // Handle case where it's already YYYY-MM-DD string
                  todoDueDateString = todo.dueDate;
               }
            } catch (e) {
               console.error("Error parsing dueDate for priority check:", todo.dueDate, e);
            }

            if (todoDueDateString === todayString) {
              console.log(`Todo ID ${todo.id} due today with priority ${todo.priority}, updating to P4.`);
              todo.priority = 4; // Update priority locally first
              // Add the update request to the promises array
              updatePromises.push(
                localStorageService.updateTodo(todo.id, { ...todo, priority: 4 })
                  .catch(err => {
                    console.error(`Failed to update priority for todo ${todo.id}:`, err);
                    // Optionally revert local change or handle error
                  })
              );
            }
          }
        });

        // Wait for all potential updates to complete
        await Promise.all(updatePromises);

        // Assign the potentially modified list to the ref
        todos.value = fetchedTodos;
        checkRecurringTodos(); // 检查重复todo
      } catch (error) {
        console.error('Error fetching todos:', error);
        todos.value = []; // 确保todos是一个空数组，以防出错
      }
    })

    const formattedDueDate = computed({
      get() {
        if (!selectedTodo.value || !selectedTodo.value.dueDate) {
          return null;
        }
        // Assuming dueDate might be a full ISO string or Date object
        try {
          const date = new Date(selectedTodo.value.dueDate);
          // Check if date is valid
          if (isNaN(date.getTime())) {
            // Handle potentially invalid date string from backend or initial state
            // Let's try to see if it's already YYYY-MM-DD
            if (typeof selectedTodo.value.dueDate === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(selectedTodo.value.dueDate)) {
               return selectedTodo.value.dueDate;
            }
            console.warn("Invalid date value for dueDate:", selectedTodo.value.dueDate);
            return null;
          }
          // Format to YYYY-MM-DD
          const year = date.getFullYear();
          const month = (date.getMonth() + 1).toString().padStart(2, '0');
          const day = date.getDate().toString().padStart(2, '0');
          return `${year}-${month}-${day}`;
        } catch (e) {
          console.error("Error parsing dueDate:", selectedTodo.value.dueDate, e);
          return null;
        }
      },
      set(newValue) {
        if (selectedTodo.value) {
          // newValue from el-date-picker with value-format="YYYY-MM-DD" should already be in the correct format or null
          selectedTodo.value.dueDate = newValue;
          // The @change handler on the component calls updateTodo, so no need to call it here explicitly
          updateTodo(); // Call updateTodo when the formatted date changes
        }
      }
    });

    const addTodo = () => {
      if (newTodo.value.trim() !== '') {
        const newTodoItem = {
          text: newTodo.value,
          completed: false,
          priority: newTodoPriority.value,
          repeat: newTodoRepeat.value,
          dueDate: null,
          details: '',
          tags: [],
          createdAt: new Date().toISOString(),
        };

        localStorageService.createTodo(newTodoItem)
          .then(createdTodo => {
            if (!Array.isArray(todos.value)) {
              todos.value = []
            }
            todos.value.push(createdTodo)
            newTodo.value = ''
            newTag.value = ''
          })
          .catch(error => {
            console.error('Error adding todo:', error)
            // 确保todos.value保持为数组
            if (!Array.isArray(todos.value)) {
              todos.value = []
            }
          })
      }
    }

    const selectTodo = (todo) => {
      selectedTodo.value = todo
      newTag.value = ''
      showDetail.value = true // 在移动端显示详情页
      showFilterDrawer.value = false // 关闭筛选抽屉
    }

    // 移动端相关方法
    const closeDetail = () => {
      showDetail.value = false
      selectedTodo.value = null
    }

    const focusAddInput = () => {
      // 聚焦到添加输入框
      nextTick(() => {
        const addInput = document.querySelector('.add-input input')
        if (addInput) {
          addInput.focus()
        }
      })
    }

    const markAsCompleted = (todoToComplete) => {
      const index = todos.value.findIndex(todo => todo.id === todoToComplete.id)
      if (index !== -1) {
        const completedAt = new Date().toISOString();
        const completedDates = todoToComplete.completedDates || [];
        completedDates.push(completedAt);

        const updatedTodo = {
          ...(todoToComplete || {}),
          completed: true,
          completedAt: completedAt,
          completedDates: completedDates
        };

        localStorageService.updateTodo(todoToComplete.id, updatedTodo)
          .then(() => {
            todos.value[index].completed = true;
            todos.value[index].completedAt = completedAt;
            todos.value[index].completedDates = completedDates;
            // Trigger reactivity for sorting
            todos.value = [...todos.value]
          })
          .catch(error => {
            console.error('Error marking todo as completed:', error)
          })
      }
    }

    const redoTodo = (todoToRedo) => {
      const index = todos.value.findIndex(todo => todo.id === todoToRedo.id)
      if (index !== -1) {
        const today = new Date().toISOString().split('T')[0];
        const updatedDates = (todoToRedo.completedDates || [])
          .filter(date => !date.startsWith(today));

        // Create updated todo object with all fields
        const updatedTodo = {
          ...todoToRedo,
          completed: false,
          completedAt: null,
          completedDates: updatedDates
        };

        console.log('Sending update:', updatedTodo);

        localStorageService.updateTodo(todoToRedo.id, updatedTodo)
          .then((updatedTodoResult) => {
            console.log('Update successful:', updatedTodoResult);
            todos.value[index] = {
              ...updatedTodoResult,
              completedDates: updatedDates
            };
            // Trigger reactivity for sorting
            todos.value = [...todos.value]
          })
          .catch(error => {
            console.error('Error redoing todo:', error.message)
          })
      }
    }

    const updateTodo = () => {
      const index = todos.value.findIndex(todo => todo.id === selectedTodo.value.id)
      if (index !== -1) {
        localStorageService.updateTodo(selectedTodo.value.id, selectedTodo.value || {})
          .then(() => {
            todos.value[index] = {
              ...selectedTodo.value
            }
            // Trigger reactivity
            todos.value = [...todos.value]
          })
          .catch(error => {
            console.error('Error updating todo:', error)
          })
      }
    }

    const addTag = () => {
      if (newTag.value.trim() !== '') {
        if (!selectedTodo.value) return;
        if (!Array.isArray(selectedTodo.value.tags)) {
          selectedTodo.value.tags = [];
        }

        const updatedTodo = {
          ...(selectedTodo.value || {}),
          tags: [...selectedTodo.value.tags, newTag.value.trim()]
        };

        localStorageService.updateTodo(selectedTodo.value.id, updatedTodo)
          .then(() => {
            selectedTodo.value.tags.push(newTag.value.trim())
            newTag.value = ''
            updateTodo()
          })
          .catch(error => {
            console.error('Error adding tag:', error)
            if (!Array.isArray(selectedTodo.value.tags)) {
              selectedTodo.value.tags = []
            }
          })
      }
    }

    const deleteTodo = (todoToDelete) => {
      const index = todos.value.findIndex(todo => todo.id === todoToDelete.id)
      if (index !== -1) {
        localStorageService.deleteTodo(todoToDelete.id)
          .then(() => {
            todos.value.splice(index, 1)
            if (selectedTodo.value && selectedTodo.value.id === todoToDelete.id) {
              selectedTodo.value = null
            }
          })
          .catch(error => {
            console.error('Error deleting todo:', error)
          })
      }
    }

    const removeTag = (index) => {
        if (!selectedTodo.value) return;
        if (!Array.isArray(selectedTodo.value.tags)) {
          selectedTodo.value.tags = [];
          return;
        }
        if (index === -1 || index >= selectedTodo.value.tags.length) return;

        const updatedTodo = {
          ...(selectedTodo.value || {}),
          tags: selectedTodo.value.tags.filter((_, i) => i !== index)
        };

        localStorageService.updateTodo(selectedTodo.value.id, updatedTodo)
          .then(() => {
            selectedTodo.value.tags.splice(index, 1);
            // Trigger reactivity manually
            selectedTodo.value = { ...selectedTodo.value };
            updateTodo();
          })
          .catch(error => {
            console.error('Error removing tag:', error);
            if (!Array.isArray(selectedTodo.value.tags)) {
              selectedTodo.value.tags = []
            }
          });
    }

    const handleFilterSelect = (index) => {
      activeFilterIndex.value = index
    }

    const getPriorityTagType = (priority) => {
      switch (priority) {
        case 5: return 'danger';  // P5 - Red
        case 4: return 'warning'; // P4 - Orange
        case 3: return 'primary'; // P3 - Blue (Default Theme Color)
        case 2: return 'success'; // P2 - Green
        case 1: return 'info';    // P1 - Gray
        default: return '';       // Default/Fallback
      }
    }

    const calendarDate = ref(new Date());
    const showCompletionHistory = ref(false); // 控制完成历史显示
    const activeNames = ref(['1']); // 控制基础设置折叠面板，默认展开

    const isDateCompleted = (dateString) => {
      if (!selectedTodo.value?.completedDates?.length) return false;
      const date = new Date(dateString);
      return selectedTodo.value.completedDates.some(completedAt => {
        const completedDate = new Date(completedAt);
        return (
          date.getFullYear() === completedDate.getFullYear() &&
          date.getMonth() === completedDate.getMonth() &&
          date.getDate() === completedDate.getDate()
        );
      });
    };

    const isCreatedDate = (dateString) => {
      if (!selectedTodo.value?.createdAt) return false;
      const date = new Date(dateString);
      const createdDate = new Date(selectedTodo.value.createdAt);
      return (
        date.getFullYear() === createdDate.getFullYear() &&
        date.getMonth() === createdDate.getMonth() &&
        date.getDate() === createdDate.getDate()
      );
    };

    const currentDateDisplay = computed(() => {
      const now = new Date();
      const options = { month: 'long', day: 'numeric', weekday: 'long' };
      return now.toLocaleDateString('zh-CN', options);
    });

    const handleTabKey = (event) => {
      if (event.key === 'Tab') {
        event.preventDefault();
        const start = event.target.selectionStart;
        const end = event.target.selectionEnd;
        const value = selectedTodo.value.details || '';
        selectedTodo.value.details = value.substring(0, start) + '\t' + value.substring(end);
        // 移动光标到插入位置后
        nextTick(() => {
          event.target.selectionStart = event.target.selectionEnd = start + 1;
        });
      }
    };

    return {
      // 注册组件
      ElDatePicker,
      ElCalendar,
      newTodo,
      newTodoPriority,
      newTodoRepeat,
      repeatOptions,
      todos,
      selectedTodo, // Expose selectedTodo
      addTodo,
      selectTodo, // Expose selectTodo
      markAsCompleted, // Expose markAsCompleted
      redoTodo, // Expose redoTodo

      // 移动端相关状态和方法
      showFilterDrawer,
      showDetail,
      showAddOptions,
      closeDetail,
      focusAddInput,

      sortedTodos: computed(() => {
        if (!Array.isArray(todos.value)) return [];

        let filtered = [...todos.value];
        const currentSearchTerm = searchTerm.value.trim().toLowerCase();

        // 1. Apply left menu filter
        if (activeFilterIndex.value === '3') { // 重要 (P4+)
          filtered = filtered.filter(todo => todo.priority >= 4 && !todo.completed);
        } else if (activeFilterIndex.value === '5') { // 不重要 (P3-)
          filtered = filtered.filter(todo => todo.priority <= 3 && !todo.completed);
        } else if (activeFilterIndex.value === '4') { // 已完成 (All completed, regardless of date)
          filtered = filtered.filter(todo => todo.completed);
        } else if (activeFilterIndex.value === '2') { // 所有待办 (Uncompleted + Completed Today)
           filtered = filtered.filter(todo =>
             !todo.completed
           );
        }
        // else: index '1' is the sidebar search, not the main content search.

        // 2. Apply search term filter (if searchTerm is not empty)
        if (currentSearchTerm) {
          filtered = filtered.filter(todo => {
            const searchText = todo.text.toLowerCase();
            const searchPriority = `p${todo.priority}`;
            const searchTags = todo.tags ? todo.tags.map(tag => tag.toLowerCase()) : []; // Handle case where tags might be null/undefined initially

            return (
              searchText.includes(currentSearchTerm) ||
              searchPriority === currentSearchTerm || // Exact match for priority like "p3"
              searchTags.some(tag => tag.includes(currentSearchTerm)) // Check if any tag includes the search term
            );
          });
        }

        // 3. Sort the filtered list
        return filtered.sort((a, b) => {
          if (activeFilterIndex.value === '4') {
            // Sort completed items by completedAt descending (newest first)
            const dateA = a.completedAt ? new Date(a.completedAt).getTime() : 0;
            const dateB = b.completedAt ? new Date(b.completedAt).getTime() : 0;
            return dateB - dateA;
          } else {
            // Default sort: uncompleted first, then by priority descending
            if (a.completed !== b.completed) {
              return a.completed ? 1 : -1; // Uncompleted items come first
            }
            return b.priority - a.priority; // Higher priority first for items with same completion status
          }
        });
      }), // Expose sortedTodos
      updateTodo, // Expose updateTodo,
      newTag,
      searchTerm, // Expose searchTerm
      currentDateDisplay, // Expose computed date display
      activeFilterIndex, // Expose activeFilterIndex
      handleFilterSelect, // Expose handleFilterSelect
      getPriorityTagType, // Expose the new method
      addTag,
      removeTag, // Expose removeTag
      deleteTodo, // Expose deleteTodo
      calendarDate,
      isDateCompleted,
      isCreatedDate,
      showCompletionHistory,
      activeNames,
      formattedDueDate, // Expose formattedDueDate
      handleTabKey // Expose handleTabKey
    }
  },
  methods: {
  }
})
</script>

<style>
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", Arial, sans-serif;
  background-color: #f5f7fa;
}

/* 移动端主容器 */
.mobile-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

/* 移动端顶部导航栏 */
.mobile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.menu-button {
  padding: 8px !important;
  color: #409eff !important;
}

.app-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-right .current-date {
  font-size: 12px;
  color: #909399;
}

/* 移动端菜单样式 */
.mobile-menu {
  border: none;
}

.mobile-menu .el-menu-item {
  padding: 16px 20px;
  font-size: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.mobile-menu .el-menu-item span {
  margin-left: 12px;
}
/* 主内容区域 */
.mobile-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.mobile-main.detail-open {
  transform: translateX(-100%);
}

/* 搜索容器 */
.search-container {
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.search-input {
  width: 100%;
}

/* 待办事项列表 */
.todo-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
  background-color: #f5f7fa;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #909399;
}

.empty-state p {
  margin: 16px 0 8px;
  font-size: 16px;
}

.empty-hint {
  font-size: 14px;
  color: #c0c4cc;
}

/* 移动端待办事项卡片 */
.mobile-todo-item {
  background-color: #fff;
  margin: 8px 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.mobile-todo-item:active {
  transform: scale(0.98);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.todo-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

.todo-main {
  flex: 1;
  min-width: 0;
}

.todo-text {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1.4;
  word-break: break-word;
}

.todo-text.completed {
  text-decoration: line-through;
  color: #909399;
}

.todo-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.priority-tag {
  font-size: 12px;
  height: 20px;
  line-height: 18px;
}

.repeat-tag {
  font-size: 12px;
  height: 20px;
  line-height: 18px;
}

.created-date {
  font-size: 12px;
  color: #909399;
}

.todo-tags {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
}

.tag-item {
  font-size: 12px;
  height: 20px;
  line-height: 18px;
}

.more-tags {
  font-size: 12px;
  color: #909399;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.todo-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.todo-actions .el-button {
  width: 32px;
  height: 32px;
  padding: 0;
}

/* 移动端添加待办事项 */
.mobile-add-todo {
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
  padding: 16px;
}

.add-todo-main {
  margin-bottom: 12px;
}

.add-input {
  width: 100%;
}

.add-button {
  border: none;
  background: none;
  color: #409eff;
  padding: 0 8px;
}

.add-button:hover {
  background-color: #ecf5ff;
}

.add-todo-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.option-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.option-row label {
  width: 60px;
  font-size: 14px;
  color: #606266;
}

.option-row .el-select {
  flex: 1;
}

.add-todo-toggle {
  text-align: center;
}

.options-toggle {
  color: #909399 !important;
  font-size: 14px;
}

/* 移动端详情页面 */
.mobile-detail {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.mobile-detail.show {
  transform: translateX(0);
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-button {
  padding: 8px !important;
  color: #409eff !important;
}

.detail-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.detail-actions {
  display: flex;
  gap: 8px;
}

.detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #606266;
}

.todo-title {
  line-height: 1.4;
  word-break: break-word;
}

.todo-title.completed {
  text-decoration: line-through;
  color: #909399;
}

.detail-meta {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.meta-item:last-child {
  margin-bottom: 0;
}

.meta-label {
  font-size: 14px;
  color: #909399;
}

.meta-value {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.setting-item label {
  font-size: 14px;
  color: #606266;
  width: 80px;
}

.setting-item .el-select {
  width: 120px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.tag-input {
  min-width: 120px;
}

.detail-textarea {
  width: 100%;
  margin-top: 8px;
}

.calendar-container {
  margin-top: 12px;
}

.mobile-calendar {
  width: 100%;
}

/* 浮动操作按钮 */
.fab {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  z-index: 100;
  border: none;
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.5);
}

/* 日历样式 */
.calendar-day {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.completion-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #67C23A;
  margin-top: 2px;
}

.creation-dot {
  background-color: rgba(64, 158, 255, 0.15);
  border-radius: 4px;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1px solid rgba(64, 158, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mobile-detail {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    transform: translateX(0);
  }

  .mobile-main.detail-open {
    display: none;
  }

  .fab {
    bottom: 20px;
    right: 20px;
    width: 48px;
    height: 48px;
  }
}

/* Element Plus 组件样式覆盖 */
.el-drawer__header {
  margin-bottom: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.el-drawer__body {
  padding: 0;
}

.el-input__wrapper {
  border-radius: 8px;
}

.el-button {
  border-radius: 6px;
}

.el-tag {
  border-radius: 4px;
}

.el-collapse-item__header {
  padding-left: 0;
  padding-right: 0;
}

.el-collapse-item__content {
  padding-bottom: 0;
}
</style>
