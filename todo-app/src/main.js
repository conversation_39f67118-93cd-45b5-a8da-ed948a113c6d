import { createApp } from 'vue';
import App from './App.vue';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import router from './router'; // Import the router

// 创建Vue应用
const app = createApp(App);

// 使用ElementPlus
app.use(ElementPlus);

// 使用Vue Router
app.use(router);

// 挂载应用
app.mount('#app');

// 如果在Electron环境中，可以添加与Electron的通信
if (window.electronAPI) {
  console.log('Running in Electron environment');

  // 监听来自主进程的消息
  window.electronAPI.receive('message-from-main', (message) => {
    console.log('Received message from main process:', message);
  });
}
