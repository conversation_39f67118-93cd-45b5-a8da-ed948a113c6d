<template>
  <el-container direction="vertical" style="flex-grow: 1;">
    <el-main>
      <el-input
        v-model="searchTerm"
        placeholder="按内容、优先级(P1-P5)或标签搜索..."
        clearable
        style="margin-bottom: 15px;"
      ></el-input>
      <div class="todo-list-container">
        <div v-for="todo in sortedTodos" :key="todo.id" class="todo-item"
             @click="goToTodoDetail(todo)">
          <div :class="{ 'completed-todo': todo.completed }">
            <span style="margin-left: 10px;">{{ todo.text }}</span>
            <el-tag size="small" :type="getPriorityTagType(todo.priority)" class="priority-tag" style="margin-left: 10px;">P{{ todo.priority }}</el-tag>
            <el-tag v-if="todo.repeat !== '不重复'" size="small" type="info" class="repeat-tag" style="margin-left: 5px;">{{ todo.repeat }}</el-tag>
            <el-tag v-for="(tag, index) in todo.tags" :key="index" type="info" style="margin-left: 5px">{{ tag }}</el-tag>
            <el-tag size="small" type="info" style="margin-left: 5px;">创建于: {{ new Date(todo.createdAt).toLocaleDateString('zh-CN') }}</el-tag>
            <el-tag v-if="todo.completed && todo.completedAt" size="small" type="success" style="margin-left: 5px;">完成于: {{ new Date(todo.completedAt).toLocaleString('zh-CN') }}</el-tag>
          </div>
          <div class="todo-buttons">
            <el-button v-if="!todo.completed" type="success" size="small" @click.stop="markAsCompleted(todo)">完成</el-button>
            <el-button v-if="todo.completed" type="warning" size="small" @click.stop="redoTodo(todo)">重做</el-button>
            <el-button type="danger" size="small" @click.stop="deleteTodo(todo)">删除</el-button>
          </div>
        </div>
      </div>
    </el-main>
    <div class="add-todo-container">
      <el-input v-model="newTodo" placeholder="添加新的待办事项" @keyup.enter="addTodo" class="add-todo-input-main"></el-input>
      <el-select v-model="newTodoPriority" placeholder="优先级" style="width: 100px; margin-left: 10px;">
        <el-option v-for="item in 5" :key="item" :label="`P${item}`" :value="item"></el-option>
      </el-select>
      <el-select v-model="newTodoRepeat" placeholder="重复" style="width: 120px; margin-left: 10px;">
        <el-option v-for="item in repeatOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
  </el-container>
</template>

<script>
import { defineComponent, ref, onMounted, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import localStorageService from '../services/localStorageService';

export default defineComponent({
  name: 'TodoListView',
  setup() {
    const router = useRouter();
    const route = useRoute();
    const newTodo = ref('');
    const newTodoPriority = ref(4); // Changed default priority to P4
    const newTodoRepeat = ref('不重复');
    const searchTerm = ref('');

    const repeatOptions = ref([
      { value: '不重复', label: '不重复' },
      { value: '工作日重复', label: '工作日重复' },
      { value: '每日重复', label: '每日重复' },
      { value: '周末重复', label: '周末重复' },
    ]);
    const todos = ref([]);

    const isWeekday = (date) => {
      const day = date.getDay();
      return day >= 1 && day <= 5;
    };

    const checkRecurringTodos = () => {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      todos.value.forEach(todo => {
        if (todo.completed && todo.completedAt) {
          const completedDate = new Date(todo.completedAt);
          completedDate.setHours(0, 0, 0, 0);

          let shouldReset = false;

          if (todo.repeat === '每日重复' && completedDate < today) {
            shouldReset = true;
          } else if (todo.repeat === '工作日重复' && completedDate < today && isWeekday(today)) {
            shouldReset = true;
          } else if (todo.repeat === '周末重复' && completedDate < today && !isWeekday(today)) {
            shouldReset = true;
          }

          if (shouldReset) {
            const updatedTodo = {
              ...todo,
              completed: false,
              completedAt: null
            };

            localStorageService.updateTodo(todo.id, updatedTodo)
              .then(() => {
                const index = todos.value.findIndex(t => t.id === todo.id);
                if (index !== -1) {
                  todos.value[index] = {
                    ...todos.value[index],
                    completed: false,
                    completedAt: null
                  };
                  todos.value = [...todos.value];
                }
              })
              .catch(error => {
                console.error('Error resetting recurring todo:', error);
              });
          }
        }
      });
    };

    onMounted(async () => {
      try {
        const fetchedTodos = await localStorageService.getAllTodos();
        const today = new Date();
        const todayString = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`;

        const updatePromises = [];

        fetchedTodos.forEach(todo => {
          if (todo.dueDate && todo.priority < 4) {
            let todoDueDateString = null;
            try {
              const dueDate = new Date(todo.dueDate);
               if (!isNaN(dueDate.getTime())) {
                  todoDueDateString = `${dueDate.getFullYear()}-${(dueDate.getMonth() + 1).toString().padStart(2, '0')}-${dueDate.getDate().toString().padStart(2, '0')}`;
               } else if (typeof todo.dueDate === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(todo.dueDate)) {
                  todoDueDateString = todo.dueDate;
               }
            } catch (e) {
               console.error("Error parsing dueDate for priority check:", todo.dueDate, e);
            }

            if (todoDueDateString === todayString) {
              console.log(`Todo ID ${todo.id} due today with priority ${todo.priority}, updating to P4.`);
              todo.priority = 4;
              updatePromises.push(
                localStorageService.updateTodo(todo.id, { ...todo, priority: 4 })
                  .catch(err => {
                    console.error(`Failed to update priority for todo ${todo.id}:`, err);
                  })
              );
            }
          }
        });

        await Promise.all(updatePromises);

        todos.value = fetchedTodos;
        checkRecurringTodos();
      } catch (error) {
        console.error('Error fetching todos:', error);
        todos.value = [];
      }
    });

    watch(() => route.query.filter, async (newFilter) => {
      console.log('Filter changed to:', newFilter);
    }, { immediate: true });

    const addTodo = () => {
      if (newTodo.value.trim() !== '') {
        const newTodoItem = {
          text: newTodo.value,
          completed: false,
          priority: newTodoPriority.value,
          repeat: newTodoRepeat.value,
          dueDate: null,
          details: '',
          tags: [],
          createdAt: new Date().toISOString(),
        };

        localStorageService.createTodo(newTodoItem)
          .then(createdTodo => {
            if (!Array.isArray(todos.value)) {
              todos.value = [];
            }
            todos.value.push(createdTodo);
            newTodo.value = '';
          })
          .catch(error => {
            console.error('Error adding todo:', error);
            if (!Array.isArray(todos.value)) {
              todos.value = [];
            }
          });
      }
    };

    const markAsCompleted = (todoToComplete) => {
      const index = todos.value.findIndex(todo => todo.id === todoToComplete.id);
      if (index !== -1) {
        const completedAt = new Date().toISOString();
        const completedDates = todoToComplete.completedDates || [];
        completedDates.push(completedAt);

        const updatedTodo = {
          ...(todoToComplete || {}),
          completed: true,
          completedAt: completedAt,
          completedDates: completedDates
        };

        localStorageService.updateTodo(todoToComplete.id, updatedTodo)
          .then(() => {
            todos.value[index].completed = true;
            todos.value[index].completedAt = completedAt;
            todos.value[index].completedDates = completedDates;
            todos.value = [...todos.value];
          })
          .catch(error => {
            console.error('Error marking todo as completed:', error);
          });
      }
    };

    const redoTodo = (todoToRedo) => {
      const index = todos.value.findIndex(todo => todo.id === todoToRedo.id);
      if (index !== -1) {
        const today = new Date().toISOString().split('T')[0];
        const updatedDates = (todoToRedo.completedDates || [])
          .filter(date => !date.startsWith(today));

        const updatedTodo = {
          ...todoToRedo,
          completed: false,
          completedAt: null,
          completedDates: updatedDates
        };

        localStorageService.updateTodo(todoToRedo.id, updatedTodo)
          .then((updatedTodoResult) => {
            todos.value[index] = {
              ...updatedTodoResult,
              completedDates: updatedDates
            };
            todos.value = [...todos.value];
          })
          .catch(error => {
            console.error('Error redoing todo:', error.message);
          });
      }
    };

    const deleteTodo = (todoToDelete) => {
      const index = todos.value.findIndex(todo => todo.id === todoToDelete.id);
      if (index !== -1) {
        localStorageService.deleteTodo(todoToDelete.id)
          .then(() => {
            todos.value.splice(index, 1);
          })
          .catch(error => {
            console.error('Error deleting todo:', error);
          });
      }
    };

    const getPriorityTagType = (priority) => {
      switch (priority) {
        case 5: return 'danger';
        case 4: return 'warning';
        case 3: return 'primary';
        case 2: return 'success';
        case 1: return 'info';
        default: return '';
      }
    };

    const currentDateDisplay = computed(() => {
      const now = new Date();
      const options = { month: 'long', day: 'numeric', weekday: 'long' };
      return now.toLocaleDateString('zh-CN', options);
    });

    const goToTodoDetail = (todo) => {
      router.push({ name: 'TodoDetail', params: { id: todo.id }, query: { todo: JSON.stringify(todo) } });
    };

    return {
      newTodo,
      newTodoPriority,
      newTodoRepeat,
      repeatOptions,
      todos,
      searchTerm,
      addTodo,
      markAsCompleted,
      redoTodo,
      deleteTodo,
      getPriorityTagType,
      currentDateDisplay,
      goToTodoDetail,
      sortedTodos: computed(() => {
        if (!Array.isArray(todos.value)) return [];

        let filtered = [...todos.value];
        const currentSearchTerm = searchTerm.value.trim().toLowerCase();
        const currentFilter = route.query.filter || 'incomplete';

        if (currentFilter === 'incomplete') {
          filtered = filtered.filter(todo => !todo.completed);
        } else if (currentFilter === 'important') {
          filtered = filtered.filter(todo => todo.priority >= 4 && !todo.completed);
        } else if (currentFilter === 'not-important') {
          filtered = filtered.filter(todo => todo.priority <= 3 && !todo.completed);
        } else if (currentFilter === 'completed') {
          filtered = filtered.filter(todo => todo.completed);
        } else { // If filter is 'all' or any other unrecognized filter, show all incomplete
          filtered = filtered.filter(todo => !todo.completed);
        }

        if (currentSearchTerm) {
          filtered = filtered.filter(todo => {
            const searchText = todo.text.toLowerCase();
            const searchPriority = `p${todo.priority}`;
            const searchTags = todo.tags ? todo.tags.map(tag => tag.toLowerCase()) : [];

            return (
              searchText.includes(currentSearchTerm) ||
              searchPriority === currentSearchTerm ||
              searchTags.some(tag => tag.includes(currentSearchTerm))
            );
          });
        }

        return filtered.sort((a, b) => {
          if (currentFilter === 'completed') {
            const dateA = a.completedAt ? new Date(a.completedAt).getTime() : 0;
            const dateB = b.completedAt ? new Date(b.completedAt).getTime() : 0;
            return dateB - dateA;
          } else {
            if (a.completed !== b.completed) {
              return a.completed ? 1 : -1;
            }
            return b.priority - a.priority;
          }
        });
      }),
    };
  },
});
</script>

<style scoped>
.el-container {
  height: 100vh;
}

.el-main {
  background-color: #f9f9f9; /* Slightly off-white for content area */
  color: #333;
  text-align: left;
  flex-grow: 1;
  overflow-y: auto;
  padding: 15px 20px; /* Add overall padding to the main content */
}

.completed-todo span {
  text-decoration: line-through;
}

.selected-todo {
  background-color: #f5f7fa;
  border-left: 3px solid #409EFF;
}

.todo-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px; /* Increased vertical padding */
  border-bottom: 1px solid #eee;
  background-color: #fff; /* White background for each item */
  border-radius: 8px; /* Subtle rounded corners */
  margin-bottom: 10px; /* Space between items */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* Subtle shadow */
}

.todo-buttons {
  display: flex;
  align-items: center;
}

.todo-buttons .el-button {
  margin-left: 5px;
}

.add-todo-container {
  display: flex;
  padding: 20px;
  border-top: 1px solid #eee;
  background-color: #fff; /* White background for the input area */
  flex-shrink: 0;
  align-items: center; /* Align items vertically */
}

.add-todo-input-main {
  flex-grow: 1;
}

.priority-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 22px;
  padding: 0 8px;
  font-size: 12px;
  line-height: 20px;
  border-radius: 4px;
}

.todo-list-container {
  width: 100%;
  padding: 0 0px; /* Remove horizontal padding here, handled by el-main */
  overflow-y: auto;
}
</style>
