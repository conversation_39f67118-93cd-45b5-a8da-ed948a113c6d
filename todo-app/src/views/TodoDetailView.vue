<template>
  <el-container direction="vertical" style="height: 100vh;">
    <el-header
      style="
        text-align: left;
        font-size: 16px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #ccc;
        flex-shrink: 0;
      "
    >
      <el-button type="default" @click="goBack" style="margin-right: 10px;">
        <el-icon><Back /></el-icon>
      </el-button>
      <h2 v-if="todo" style="flex-grow: 1; text-align: center;">{{ todo.text }}</h2>
      <h2 v-else>加载中...</h2>
    </el-header>
    <el-main style="padding: 20px; overflow-y: auto;">
      <div v-if="todo">
        <el-collapse v-model="activeNames">
          <el-collapse-item title="基础设置" name="1">
            <div class="detail-edit-item">
              <span>优先级: </span>
              <el-select v-model="todo.priority" @change="updateTodo" placeholder="优先级" style="width: 120px;">
                <el-option v-for="item in 5" :key="item" :label="`P${item}`" :value="item"></el-option>
              </el-select>
            </div>
            <div class="detail-edit-item">
              <span>重复: </span>
              <el-select v-model="todo.repeat" @change="updateTodo" placeholder="重复" style="width: 150px;">
                <el-option v-for="item in repeatOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>
            <div class="detail-edit-item-date">
              <span class="detail-edit-item-date-span">截止日期: </span>
              <el-date-picker v-model="formattedDueDate" type="date" placeholder="选择日期" format="YYYY-MM-DD"
                value-format="YYYY-MM-DD" style="width: 150px;display: inline-flex;"></el-date-picker>
            </div>
            <div class="detail-edit-item">
              <span>标签: </span>
              <el-input v-model="newTag" @keyup.enter="addTag" placeholder="添加标签" style="width: 150px;"></el-input>
              <el-tag
                v-for="(tag, index) in todo.tags"
                :key="index"
                class="mx-1"
                type=""
                closable
                @close="removeTag(index)"
                style="margin-left: 5px;display: inline-flex;"
              >
                {{ tag }}
              </el-tag>
            </div>
          </el-collapse-item>
        </el-collapse>
       
          <el-collapse v-model="showCompletionHistory">
            <el-collapse-item title="  完成历史" name="1">
              <el-calendar v-model="calendarDate">
                <template #date-cell="{data}">
                  <div class="calendar-day">
                    {{ data.day.split('-').slice(2).join('-') }}
                    <div v-if="isDateCompleted(data.day)" class="completion-dot"></div>
                    <div v-if="isCreatedDate(data.day)" class="creation-dot"></div>
                  </div>
                </template>
              </el-calendar>
            </el-collapse-item>
          </el-collapse>
        
        <div class="detail-edit-item">
          <el-input v-model="todo.details" @change="updateTodo" type="textarea" placeholder="输入详情"
            class="detail-textarea" tabindex="0" @keydown="handleTabKey"></el-input>
        </div>
      </div>
      <div v-else>
        <p>加载待办事项详情...</p>
      </div>
    </el-main>
  </el-container>
</template>

<script>
import { defineComponent, ref, onMounted, computed, watch, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import localStorageService from '../services/localStorageService';
import { Back } from '@element-plus/icons-vue';

export default defineComponent({
  name: 'TodoDetailView',
  props: {
    id: {
      type: String,
      required: true,
    },
  },
  setup(props) {
    const route = useRoute();
    const router = useRouter();
    const todo = ref(null);
    const newTag = ref('');
    const calendarDate = ref(new Date());
    const showCompletionHistory = ref(false);
    const activeNames = ref(['1']);

    const repeatOptions = ref([
      { value: '不重复', label: '不重复' },
      { value: '工作日重复', label: '工作日重复' },
      { value: '每日重复', label: '每日重复' },
      { value: '周末重复', label: '周末重复' },
    ]);

    const fetchTodo = async (todoId) => {
      try {
        // Try to get from route query first (if passed from TodoListView)
        if (route.query.todo) {
          todo.value = JSON.parse(route.query.todo);
        } else {
          // Fallback to fetching from localStorageService if not in query
          const fetchedTodo = await localStorageService.getTodoById(todoId);
          todo.value = fetchedTodo;
        }
      } catch (error) {
        console.error('Error fetching todo details:', error);
        todo.value = null;
      }
    };

    onMounted(() => {
      fetchTodo(props.id);
    });

    watch(() => props.id, (newId) => {
      fetchTodo(newId);
    });

    const formattedDueDate = computed({
      get() {
        if (!todo.value || !todo.value.dueDate) {
          return null;
        }
        try {
          const date = new Date(todo.value.dueDate);
          if (isNaN(date.getTime())) {
            if (typeof todo.value.dueDate === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(todo.value.dueDate)) {
               return todo.value.dueDate;
            }
            console.warn("Invalid date value for dueDate:", todo.value.dueDate);
            return null;
          }
          const year = date.getFullYear();
          const month = (date.getMonth() + 1).toString().padStart(2, '0');
          const day = date.getDate().toString().padStart(2, '0');
          return `${year}-${month}-${day}`;
        } catch (e) {
          console.error("Error parsing dueDate:", todo.value.dueDate, e);
          return null;
        }
      },
      set(newValue) {
        if (todo.value) {
          todo.value.dueDate = newValue;
          updateTodo();
        }
      }
    });

    const updateTodo = () => {
      if (todo.value) {
        localStorageService.updateTodo(todo.value.id, todo.value)
          .then(() => {
            console.log('Todo updated successfully');
          })
          .catch(error => {
            console.error('Error updating todo:', error);
          });
      }
    };

    const addTag = () => {
      if (newTag.value.trim() !== '' && todo.value) {
        if (!Array.isArray(todo.value.tags)) {
          todo.value.tags = [];
        }
        const updatedTodo = {
          ...(todo.value || {}),
          tags: [...todo.value.tags, newTag.value.trim()]
        };
        localStorageService.updateTodo(todo.value.id, updatedTodo)
          .then(() => {
            todo.value.tags.push(newTag.value.trim());
            newTag.value = '';
            // No need to call updateTodo again, as the promise handles the update
          })
          .catch(error => {
            console.error('Error adding tag:', error);
          });
      }
    };

    const removeTag = (index) => {
      if (!todo.value || !Array.isArray(todo.value.tags) || index === -1 || index >= todo.value.tags.length) {
        return;
      }
      const updatedTodo = {
        ...(todo.value || {}),
        tags: todo.value.tags.filter((_, i) => i !== index)
      };
      localStorageService.updateTodo(todo.value.id, updatedTodo)
        .then(() => {
          todo.value.tags.splice(index, 1);
          // Trigger reactivity manually if needed, though splice should be reactive
          todo.value = { ...todo.value };
        })
        .catch(error => {
          console.error('Error removing tag:', error);
        });
    };

    const isDateCompleted = (dateString) => {
      if (!todo.value?.completedDates?.length) return false;
      const date = new Date(dateString);
      return todo.value.completedDates.some(completedAt => {
        const completedDate = new Date(completedAt);
        return (
          date.getFullYear() === completedDate.getFullYear() &&
          date.getMonth() === completedDate.getMonth() &&
          date.getDate() === completedDate.getDate()
        );
      });
    };

    const isCreatedDate = (dateString) => {
      if (!todo.value?.createdAt) return false;
      const date = new Date(dateString);
      const createdDate = new Date(todo.value.createdAt);
      return (
        date.getFullYear() === createdDate.getFullYear() &&
        date.getMonth() === createdDate.getMonth() &&
        date.getDate() === createdDate.getDate()
      );
    };

    const goBack = () => {
      router.back(); // Go back to the previous page
    };

    const handleTabKey = (event) => {
      if (event.key === 'Tab') {
        event.preventDefault();
        const start = event.target.selectionStart;
        const end = event.target.selectionEnd;
        const value = todo.value.details || '';
        todo.value.details = value.substring(0, start) + '\t' + value.substring(end);
        nextTick(() => {
          event.target.selectionStart = event.target.selectionEnd = start + 1;
        });
      }
    };

    return {
      todo,
      newTag,
      repeatOptions,
      calendarDate,
      showCompletionHistory,
      activeNames,
      formattedDueDate,
      updateTodo,
      addTag,
      removeTag,
      isDateCompleted,
      isCreatedDate,
      goBack,
      handleTabKey,
      Back, // Expose Back component
    };
  },
});
</script>

<style scoped>
.el-container {
  background-color: #fafafa;
}

.el-header {
  background-color: #f5f7fa;
  color: #333;
  padding: 0 20px;
}

.el-header h2 {
  margin: 0;
  font-size: 18px;
}

.el-main {
  background-color: #f9f9f9; /* Slightly off-white for content area */
  color: #333;
  text-align: left;
  padding: 20px; /* Ensure consistent padding */
}

.detail-edit-item-date,
.detail-edit-item {
  display: flex; /* Use flexbox for alignment */
  align-items: center;
  margin-bottom: 15px;
  padding: 10px 15px; /* Add padding to each item */
  background-color: #fff; /* White background for each item */
  border-radius: 8px; /* Subtle rounded corners */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* Subtle shadow */
}

.detail-edit-item-date-span,
.detail-edit-item span {
  width: 90px; /* Adjust width for labels */
  flex-shrink: 0; /* Prevent shrinking */
  color: #666;
  font-weight: bold; /* Make labels slightly bolder */
  margin-right: 10px; /* Space between label and input */
}

.calendar-day {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.completion-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #67C23A;
  margin-top: 2px;
}

.creation-dot {
  background-color: rgba(64, 158, 255, 0.15);
  border-radius: 4px;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1px solid rgba(64, 158, 255, 0.3);
}

.el-calendar {
  margin-top: 10px;
}

.el-calendar__body {
  padding: 0;
}

.detail-textarea .el-textarea__inner {
  width: 100%;
  margin-top: 10px;
  height: 600px !important; /* Set height using class and !important */
  min-height: 600px !important;
  max-height: 600px !important;
}
</style>
