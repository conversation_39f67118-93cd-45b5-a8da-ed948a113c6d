# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
package-lock.json
yarn.lock

# Vue.js
/dist/
/dist_electron/

# Electron
/build/
/release/
*.log

# Environment variables
.env
.env.local
.env.*.local

# IDE files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Debug logs
logs/
*.log

# Coverage directory used by tools like istanbul
coverage/

# Local Netlify folder
.netlify/
