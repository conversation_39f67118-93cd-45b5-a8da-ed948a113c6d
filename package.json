{"name": "todo-electron-app", "version": "1.0.0", "description": "离线待办事项应用", "main": "electron/main.js", "scripts": {"start": "electron .", "dev": "node electron/start-electron.js", "build": "cd todo-app && npm run build && cd .. && electron-builder", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["electron", "vue", "todo", "offline"], "author": "", "license": "ISC", "devDependencies": {"electron": "^30.0.0", "electron-builder": "^24.13.3"}, "build": {"appId": "com.electron.todo-app", "productName": "待办事项应用", "directories": {"output": "dist_electron"}, "files": ["electron/**/*", "todo-app/dist/**/*", "!**/node_modules/**/*"], "mac": {"category": "public.app-category.productivity", "target": [{"target": "dmg", "arch": ["x64", "arm64", "universal"]}, {"target": "zip", "arch": ["x64", "arm64", "universal"]}]}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}